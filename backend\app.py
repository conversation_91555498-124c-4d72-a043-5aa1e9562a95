from flask import Flask, request, jsonify, Response, send_from_directory
from flask_cors import CORS
import cv2
import numpy as np
import os
from werkzeug.utils import secure_filename
import time
from ultralytics import YOLO
import logging
import json
from datetime import datetime, timezone
import threading
import queue
from collections import deque
import mediapipe as mp
import tensorflow as tf
import requests
import base64
import asyncio
import aiohttp
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__, static_folder=os.path.dirname(os.path.abspath(__file__)))
# Enable CORS for all routes
CORS(app, resources={r"/*": {"origins": "*"}})

# Increase timeout for large uploads
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# Configure paths
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
OUTPUT_FOLDER = os.path.join(BASE_DIR, 'output_videos', 'upload')
REALTIME_OUTPUT_FOLDER = os.path.join(BASE_DIR, 'output_videos', 'realtime')
MODEL_PATH = os.path.join(BASE_DIR, 'weights', 'best.pt')  # Using standard YOLO model

# Create routes to serve static files
@app.route('/output_videos/upload/<path:filename>')
def serve_processed_video(filename):
    logger.info(f"Serving processed video: {filename} from {OUTPUT_FOLDER}")
    # Determine content type based on file extension
    content_type = 'video/mp4'
    if filename.lower().endswith('.avi'):
        content_type = 'video/x-msvideo'

    response = send_from_directory(OUTPUT_FOLDER, filename)
    response.headers['Content-Type'] = content_type
    response.headers['Access-Control-Allow-Origin'] = '*'
    return response

@app.route('/uploads/<path:filename>')
def serve_original_video(filename):
    logger.info(f"Serving original video: {filename} from {UPLOAD_FOLDER}")
    response = send_from_directory(UPLOAD_FOLDER, filename)
    response.headers['Content-Type'] = 'video/mp4'
    response.headers['Access-Control-Allow-Origin'] = '*'
    return response

@app.route('/output_videos/realtime/<path:filename>')
def serve_realtime_video(filename):
    logger.info(f"Serving real-time video: {filename} from {REALTIME_OUTPUT_FOLDER}")
    # Determine content type based on file extension
    content_type = 'video/mp4'
    if filename.lower().endswith('.avi'):
        content_type = 'video/x-msvideo'

    response = send_from_directory(REALTIME_OUTPUT_FOLDER, filename)
    response.headers['Content-Type'] = content_type
    response.headers['Access-Control-Allow-Origin'] = '*'
    return response

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)
os.makedirs(REALTIME_OUTPUT_FOLDER, exist_ok=True)

# Configure upload settings
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def format_timestamp(seconds):
    """Convert seconds to HH:MM:SS format"""
    return str(datetime.fromtimestamp(seconds, tz=timezone.utc).strftime('%H:%M:%S'))

@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint to check if the server is running"""
    try:
        # Check if camera is available
        camera_status = "available" if camera is not None and camera.isOpened() else "not available"

        # Check if models are loaded
        yolo_status = "loaded" if yolo_model is not None else "not loaded"
        cnn_lstm_status = "loaded" if cnn_lstm_model is not None else "not loaded"

        # Return detailed health information
        return jsonify({
            'status': 'healthy',
            'message': 'Server is running',
            'camera': camera_status,
            'yolo_model': yolo_status,
            'cnn_lstm_model': cnn_lstm_status,
            'server_time': time.strftime("%Y-%m-%d %H:%M:%S"),
            'is_recording': is_recording
        }), 200
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/upload', methods=['POST', 'OPTIONS'])
def upload_video():
    try:
        logger.info("Upload endpoint called")

        # Handle preflight CORS request
        if request.method == 'OPTIONS':
            response = jsonify({'status': 'ok'})
            response.headers.add('Access-Control-Allow-Origin', '*')
            response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
            response.headers.add('Access-Control-Allow-Methods', 'POST')
            return response

        # Log request details
        logger.info(f"Request content type: {request.content_type}")
        logger.info(f"Request files: {request.files}")

        if 'video' not in request.files:
            logger.error("No video file in request")
            return jsonify({'error': 'No video file provided'}), 400

        file = request.files['video']
        logger.info(f"Received file: {file.filename}, {file.content_type}, {file.content_length} bytes")

        if file.filename == '':
            logger.error("Empty filename")
            return jsonify({'error': 'No selected file'}), 400

        if not allowed_file(file.filename):
            logger.error(f"Invalid file type: {file.filename}")
            return jsonify({'error': 'Invalid file type. Allowed types: mp4, avi, mov'}), 400

        # Ensure directories exist
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(OUTPUT_FOLDER, exist_ok=True)

        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = int(time.time())
        input_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{timestamp}_{filename}")
        # Change output extension to .mp4 for H.264 codec
        output_filename = f"processed_{timestamp}_{os.path.splitext(filename)[0]}.mp4"
        output_path = os.path.join(OUTPUT_FOLDER, output_filename)
        json_path = os.path.join(OUTPUT_FOLDER, f"detections_{timestamp}_{filename}.json")

        logger.info(f"Saving uploaded file to: {input_path}")
        try:
            file.save(input_path)
            logger.info(f"File saved successfully. Size: {os.path.getsize(input_path)} bytes")
        except Exception as e:
            logger.error(f"Error saving file: {str(e)}")
            return jsonify({'error': f'Error saving file: {str(e)}'}), 500

        # Verify the file was saved correctly
        if not os.path.exists(input_path):
            logger.error(f"File was not saved correctly: {input_path}")
            return jsonify({'error': 'File was not saved correctly'}), 500

        # Initialize models
        logger.info("Initializing models for video processing...")
        if not initialize_models():
            logger.error("Failed to initialize models")
            return jsonify({'error': 'Failed to initialize models'}), 500

        logger.info(f"YOLO model loaded successfully with classes: {yolo_model.names}")
        logger.info("CNN+LSTM model loaded successfully")

        # Process video
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            logger.error(f"Failed to open video file: {input_path}")
            return jsonify({'error': 'Failed to open video file'}), 500

        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Initialize counters for violence detection
        violence_frame_count = 0
        non_violence_frame_count = 0

        logger.info(f"Processing video: {fps} FPS, {frame_width}x{frame_height}, {total_frames} frames")

        # Make sure the directories exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Create video writer
        try:
            # Use H.264 codec which is widely supported
            fourcc = cv2.VideoWriter_fourcc(*'avc1')
            out = cv2.VideoWriter(output_path, fourcc, 10, (frame_width, frame_height))
            if not out.isOpened():
                logger.error(f"Failed to create video writer: {output_path}")
                # Try fallback to XVID codec
                logger.info("Trying fallback to XVID codec...")
                fourcc = cv2.VideoWriter_fourcc(*'XVID')
                out = cv2.VideoWriter(output_path, fourcc, 10, (frame_width, frame_height))
                if not out.isOpened():
                    logger.error(f"Failed to create video writer with fallback codec: {output_path}")
                    return jsonify({'error': 'Failed to create video writer'}), 500
            logger.info(f"Video writer created successfully: {output_path}")
        except Exception as e:
            logger.error(f"Error creating video writer: {str(e)}")
            return jsonify({'error': f'Error creating video writer: {str(e)}'}), 500

        frame_count = 0
        processed_count = 0

        # Initialize detections list for JSON
        detections_data = {
            "filename": filename,
            "detections": []
        }

        # Variables to track the most important violence frame
        most_violent_frame = None
        highest_violence_confidence = 0.0

        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break

            # Process every Nth frame to achieve 10 fps
            if frame_count % int(fps/10) == 0:
                # Calculate current timestamp in video
                current_time = frame_count / fps

                # Perform YOLO detection
                results = yolo_model(frame)
                boxes = results[0].boxes.xyxy  # Extract the bounding boxes

                # We don't need a per-frame violence flag anymore since we're using counters
                # Just process each detection

                # Loop through the detected bounding boxes
                for box in boxes:
                    x1, y1, x2, y2 = map(int, box)

                    # Crop the detected region from the frame (for pose detection)
                    if y2 > y1 and x2 > x1:  # Ensure valid crop dimensions
                        cropped_image = frame[y1:y2, x1:x2]

                        # Convert the cropped image to RGB (MediaPipe requires RGB)
                        img_rgb = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB)

                        # Apply MediaPipe to extract pose landmarks
                        pose_results = pose.process(img_rgb)

                        is_violence = False

                        if pose_results.pose_landmarks:
                            # Extract keypoints from MediaPipe
                            keypoints = []
                            for lm in pose_results.pose_landmarks.landmark:
                                keypoints.extend([lm.x, lm.y, lm.z, lm.visibility])

                            # Reshape the keypoints into a 1D array for CNN + LSTM model
                            keypoints = np.array(keypoints).reshape(1, 33, 4)  # 33 keypoints, each with (x, y, z, visibility)

                            # Make prediction using the trained CNN + LSTM model
                            prediction = cnn_lstm_model.predict(keypoints, verbose=0)
                            is_violence = prediction[0] > VIOLENCE_THRESHOLD

                            # Count violent and non-violent detections
                            if is_violence:
                                violence_frame_count += 1  # Increment violence frame counter

                                # Check if this is the most violent frame so far
                                violence_confidence = float(prediction[0])
                                if violence_confidence > highest_violence_confidence:
                                    highest_violence_confidence = violence_confidence
                                    most_violent_frame = frame.copy()  # Store the frame for Ollama analysis
                            else:
                                non_violence_frame_count += 1  # Increment non-violence frame counter

                            # Set color based on classification (red for Violence, green for Non-Violence)
                            color = (0, 0, 255) if is_violence else (0, 255, 0)

                            # Draw bounding box with appropriate color (no text on the box)
                            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                            # Add detection to JSON data
                            # Calculate normalized bounding box coordinates
                            bbox = {
                                "x": float(x1) / frame_width,
                                "y": float(y1) / frame_height,
                                "width": float(x2 - x1) / frame_width,
                                "height": float(y2 - y1) / frame_height
                            }

                            # Get confidence value from prediction
                            confidence_value = float(prediction[0]) if is_violence else 1.0 - float(prediction[0])

                            # Create class name for JSON
                            violence_label = f"Violence: {is_violence}"

                            detection = {
                                "timestamp": format_timestamp(current_time),
                                "class": violence_label,
                                "is_violence": bool(is_violence),
                                "confidence": round(confidence_value, 2),
                                "bbox": bbox
                            }
                            detections_data["detections"].append(detection)

                # We'll add the dominant classification label later during the reprocessing step
                # For now, just count the frames with violence

                out.write(frame)
                processed_count += 1

                # Log progress every 100 frames
                if processed_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"Processing progress: {progress:.2f}%")

            frame_count += 1

        cap.release()
        out.release()

        # Determine the dominant classification
        is_video_violent = violence_frame_count >= non_violence_frame_count
        logger.info(f"Violence frames: {violence_frame_count}, Non-violence frames: {non_violence_frame_count}")
        logger.info(f"Video classification: {'Violence' if is_video_violent else 'Non-Violence'}")

        # Update the JSON data with the dominant classification
        detections_data["is_violent"] = is_video_violent
        detections_data["violence_frame_count"] = violence_frame_count
        detections_data["non_violence_frame_count"] = non_violence_frame_count

        # If violence is detected and we have a frame, get description from Ollama
        violence_description = None
        if is_video_violent and most_violent_frame is not None:
            logger.info("Violence detected! Getting description from Ollama...")
            violence_description = get_violence_description_from_ollama(most_violent_frame)
            detections_data["violence_description"] = violence_description
            logger.info(f"Ollama description: {violence_description}")

        # Save detections to JSON file
        with open(json_path, 'w') as f:
            json.dump(detections_data, f, indent=2)

        # Now reprocess the video to apply the dominant classification to all frames
        logger.info("Reprocessing video with dominant classification...")

        # Reopen the processed video
        cap = cv2.VideoCapture(output_path)
        if not cap.isOpened():
            logger.error(f"Failed to open processed video for reprocessing: {output_path}")
            # Continue without reprocessing
        else:
            # Create a temporary output file
            temp_output_path = os.path.join(OUTPUT_FOLDER, f"temp_{output_filename}")

            # Create video writer for the temporary file
            fourcc = cv2.VideoWriter_fourcc(*'avc1')
            temp_out = cv2.VideoWriter(temp_output_path, fourcc, 10, (frame_width, frame_height))

            if not temp_out.isOpened():
                logger.error(f"Failed to create temporary video writer: {temp_output_path}")
                # Try fallback to XVID codec
                logger.info("Trying fallback to XVID codec...")
                fourcc = cv2.VideoWriter_fourcc(*'XVID')
                temp_out = cv2.VideoWriter(temp_output_path, fourcc, 10, (frame_width, frame_height))

            if temp_out.isOpened():
                # Process each frame
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break

                    # Add the dominant classification label to the upper left corner
                    if is_video_violent:
                        # Create a label with Violence: True in red
                        violence_status = "Violence: True"
                        text_color = (0, 0, 255)  # Red color
                    else:
                        # Create a label with Violence: False in green
                        violence_status = "Violence: False"
                        text_color = (0, 255, 0)  # Green color

                    # Create background for text
                    text_size = cv2.getTextSize(violence_status, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)[0]
                    cv2.rectangle(frame, (10, 10), (10 + text_size[0], 10 + text_size[1] + 10), text_color, -1)

                    # Add label to upper left corner
                    cv2.putText(frame, violence_status, (15, 35),
                              cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)

                    # Write the frame to the temporary output file
                    temp_out.write(frame)

                # Release resources
                cap.release()
                temp_out.release()

                # Replace the original output file with the temporary file
                try:
                    os.remove(output_path)
                    os.rename(temp_output_path, output_path)
                    logger.info(f"Successfully reprocessed video with dominant classification")
                except Exception as e:
                    logger.error(f"Error replacing output file: {str(e)}")
            else:
                logger.error("Failed to create temporary video writer, skipping reprocessing")
                cap.release()

        # Log detection data for debugging
        logger.info(f"Total detections: {len(detections_data['detections'])}")
        if len(detections_data['detections']) > 0:
            logger.info(f"Sample detection: {detections_data['detections'][0]}")

        # Keep the input file for comparison
        logger.info("Keeping input file for comparison...")

        # Verify that the files exist and check their sizes
        if not os.path.exists(input_path):
            logger.error(f"Original video file not found: {input_path}")
        else:
            input_size = os.path.getsize(input_path)
            logger.info(f"Original video file exists: {input_path}, Size: {input_size} bytes")

        if not os.path.exists(output_path):
            logger.error(f"Processed video file not found: {output_path}")
        else:
            output_size = os.path.getsize(output_path)
            logger.info(f"Processed video file exists: {output_path}, Size: {output_size} bytes")

        # List all files in the output directory
        logger.info(f"Files in output directory {OUTPUT_FOLDER}:")
        for file in os.listdir(OUTPUT_FOLDER):
            logger.info(f"  - {file}")

        # Convert paths to relative URLs
        output_url = f'/output_videos/upload/{output_filename}'
        json_url = f'/output_videos/upload/detections_{timestamp}_{filename}.json'
        original_url = f'/uploads/{timestamp}_{filename}'

        logger.info(f"Video processing completed successfully")
        logger.info(f"Original video URL: {original_url}")
        logger.info(f"Processed video URL: {output_url}")

        response_data = {
            'message': 'Video processed successfully',
            'processed_video': output_url,
            'detections_json': json_url,
            'detections': detections_data['detections'],
            'original_video': original_url,
            'is_violent': is_video_violent
        }

        # Add violence description if available
        if violence_description:
            response_data['violence_description'] = violence_description

        return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"Error processing video: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

# Add these global variables after the existing configurations
camera = None
output_frame = None
lock = threading.Lock()
is_recording = False
video_writer = None
yolo_model = None
cnn_lstm_model = None
mp_pose = None
pose = None
last_detection_time = 0
DETECTION_INTERVAL = 0.1  # 10 frames per second (1/10 = 0.1 seconds between frames)
AUTO_RECORD_DURATION = 30  # Duration to record after violence detection (in seconds)
VIOLENCE_THRESHOLD = 0.92  # Confidence threshold for violence detection (0.92 for CNN+LSTM model)
last_violence_time = 0
auto_recording_start_time = None

# Frame queue system for real-time violence detection
FRAME_BUFFER_SIZE = 60  # 6 seconds at 10 FPS
frame_queue = deque(maxlen=FRAME_BUFFER_SIZE)  # Sliding window of frames
frame_timestamps = deque(maxlen=FRAME_BUFFER_SIZE)  # Corresponding timestamps
violence_detected_in_buffer = False
auto_recording_enabled = True  # Auto-start recording when camera opens
realtime_clips_dir = None
current_clip_detections = []
most_violent_frame_in_buffer = None
highest_violence_confidence_in_buffer = 0.0

# User session management for Telegram notifications
current_user_session = {
    'phone_number': None,
    'telegram_chat_id': None,
    'full_name': None,
    'email': None,
    'location_name': "Security Camera Location",
    'bot_token': None
}

# Simple phone to chat ID mapping (in production, use a database)
phone_to_chat_mapping = {}

# Verification codes for automatic phone-to-chat mapping
verification_codes = {}  # {verification_code: {'phone': phone_number, 'timestamp': timestamp}}

# Paths to model files
YOLO_MODEL_PATH = os.path.join(BASE_DIR, 'weights', 'best.pt')
CNN_LSTM_MODEL_PATH = os.path.join(BASE_DIR, 'weights', 'cnn_lstm_pose_weights.keras')

# Ollama API configuration
OLLAMA_API_URL = "http://localhost:11434/api/chat"
OLLAMA_MODEL = "llava:latest"  # Use llava model for image captioning

# Telegram Bot configuration - Use your provided token
TELEGRAM_BOT_TOKEN = '**********************************************'
TELEGRAM_API_URL = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}"
WEBSITE_URL = "http://localhost:5500/front%20end/realtime.html"  # URL to your website
LOCATION_NAME = "Security Camera Location"  # Default location name

def get_violence_description_from_ollama(frame):
    """
    Send frame to Ollama API for violence description using streaming API
    """
    try:
        # Convert frame to base64
        _, buffer = cv2.imencode('.jpg', frame)
        image_base64 = base64.b64encode(buffer).decode('utf-8')

        # Prepare the request payload exactly as specified
        payload = {
            "model": OLLAMA_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": "You're a security analyst. Look at this image and write a short 2-sentence report about any signs of violence, aggression, or suspicious activity — include how many people are there and what they're doing.",
                    "images": [image_base64]
                }
            ]
        }

        # Send the HTTP POST request with streaming enabled
        response = requests.post(OLLAMA_API_URL, json=payload, stream=True, timeout=60)

        # Check the response status
        if response.status_code == 200:
            logger.info("Streaming response from Ollama...")
            full_response = ''
            for line in response.iter_lines(decode_unicode=True):
                if line:  # Ignore empty lines
                    try:
                        # Parse each line as a JSON object
                        json_data = json.loads(line)
                        # Extract and accumulate the assistant's message content
                        if "message" in json_data and "content" in json_data["message"]:
                            full_response += json_data["message"]["content"]
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse line: {line}")

            if full_response.strip():
                return full_response.strip()
            else:
                return "Unable to generate description - no content received"
        else:
            logger.error(f"Ollama API error: {response.status_code}")
            logger.error(response.text)
            return "Unable to generate description due to API error"

    except Exception as e:
        logger.error(f"Error calling Ollama API: {str(e)}")
        return "Unable to generate description due to connection error"

class TelegramNotificationService:
    """Service for sending Telegram notifications for violence detection"""

    def __init__(self):
        self.bot_token = TELEGRAM_BOT_TOKEN
        self.api_url = TELEGRAM_API_URL

    def get_current_bot_token(self):
        """Get the current bot token from session or environment"""
        global current_user_session
        bot_token = current_user_session.get('bot_token', TELEGRAM_BOT_TOKEN)
        return bot_token

    def get_current_location(self):
        """Get the current location name from session"""
        global current_user_session
        return current_user_session.get('location_name', LOCATION_NAME)

    def get_user_phone_from_session(self):
        """Get the current user's phone number from session"""
        global current_user_session
        return current_user_session.get('phone_number')

    def get_chat_id_from_session(self):
        """Get the current user's Telegram chat ID from session"""
        global current_user_session, phone_to_chat_mapping

        # First try to get from session
        chat_id = current_user_session.get('telegram_chat_id')

        # If not in session, try to find by phone number
        if not chat_id:
            phone_number = current_user_session.get('phone_number')
            if phone_number and phone_number in phone_to_chat_mapping:
                chat_id = phone_to_chat_mapping[phone_number]
                # Update session with found chat_id
                current_user_session['telegram_chat_id'] = chat_id
                logger.info(f"Found chat_id {chat_id} for phone {phone_number}")

        # If still no chat_id, try to auto-discover from recent bot activity
        if not chat_id:
            phone_number = current_user_session.get('phone_number')
            if phone_number:
                chat_id = self.auto_discover_chat_id(phone_number)
                if chat_id:
                    current_user_session['telegram_chat_id'] = chat_id
                    logger.info(f"Auto-discovered chat_id {chat_id} for phone {phone_number}")

        return chat_id

    def auto_discover_chat_id(self, phone_number):
        """Automatically discover and map chat ID for a phone number"""
        global phone_to_chat_mapping

        try:
            # Get recent updates from Telegram
            bot_token = self.get_current_bot_token() or TELEGRAM_BOT_TOKEN
            if not bot_token:
                logger.error("No Telegram bot token available")
                return None

            url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
            response = requests.get(url)
            data = response.json()

            if response.status_code == 200 and data.get('ok'):
                updates = data.get('result', [])
                logger.info(f"Found {len(updates)} Telegram updates for auto-discovery")

                # Look for recent messages and try to match with phone number
                for update in reversed(updates[-20:]):  # Check last 20 updates
                    if 'message' in update:
                        chat = update['message']['chat']
                        chat_id = chat['id']
                        message_time = update['message'].get('date', 0)
                        current_time = time.time()

                        logger.info(f"Checking update: chat_id={chat_id}, time_diff={current_time - message_time}s")

                        # Try multiple matching strategies
                        # Strategy 1: Check if user shared contact with matching phone
                        if 'contact' in update['message']:
                            contact_phone = update['message']['contact'].get('phone_number', '')
                            if contact_phone and (contact_phone == phone_number or
                                                contact_phone.endswith(phone_number[-7:])):
                                phone_to_chat_mapping[phone_number] = chat_id
                                logger.info(f"Auto-mapped phone {phone_number} to chat_id {chat_id} via contact")
                                return chat_id

                        # Strategy 2: If this is a recent interaction (within last 10 minutes), assume it's our user
                        if current_time - message_time < 600:  # 10 minutes
                            # Check if this is a /start command or recent activity
                            text = update['message'].get('text', '').lower()
                            if '/start' in text or current_time - message_time < 300:  # 5 minutes for any message
                                phone_to_chat_mapping[phone_number] = chat_id
                                logger.info(f"Auto-mapped phone {phone_number} to recent chat_id {chat_id} (text: {text})")
                                return chat_id

                # Strategy 3: If no recent activity, try the most recent chat ID as fallback
                if updates:
                    latest_update = updates[-1]
                    if 'message' in latest_update:
                        chat_id = latest_update['message']['chat']['id']
                        message_time = latest_update['message'].get('date', 0)
                        current_time = time.time()

                        # Only use if within last 30 minutes
                        if current_time - message_time < 1800:  # 30 minutes
                            phone_to_chat_mapping[phone_number] = chat_id
                            logger.info(f"Auto-mapped phone {phone_number} to latest chat_id {chat_id} as fallback")
                            return chat_id

            else:
                logger.error(f"Telegram API error: {data.get('description', 'Unknown error')}")

        except Exception as e:
            logger.error(f"Error auto-discovering chat ID: {str(e)}")

        return None

    async def send_emergency_alert(self, violence_description, clip_info=None):
        """Send emergency violence detection alert via Telegram"""
        try:
            # Get user phone number from session
            phone_number = self.get_user_phone_from_session()
            if not phone_number:
                logger.warning("No phone number found in session for Telegram notification")
                return False

            # Get chat ID from session
            chat_id = self.get_chat_id_from_session()
            if not chat_id:
                # Try to auto-discover chat ID
                logger.info(f"Attempting to auto-discover chat ID for phone: {phone_number}")
                chat_id = self.auto_discover_chat_id(phone_number)

            if not chat_id:
                logger.warning(f"No Telegram chat ID found for phone number: {phone_number}")
                return False

            # Format the emergency message
            current_time = datetime.now()
            formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            formatted_date = current_time.strftime("%B %d, %Y")
            location_name = self.get_current_location()

            # Create comprehensive alert message
            alert_id = f"VG{int(current_time.timestamp())}"
            message = f"""🚨 **VISION GUARD EMERGENCY ALERT** 🚨

⚠️ **VIOLENCE DETECTED - IMMEDIATE ATTENTION REQUIRED** ⚠️

🆔 **Alert ID:** {alert_id}
📍 **Location:** {location_name}
📅 **Date:** {formatted_date}
🕐 **Time:** {formatted_time}

🤖 **AI SECURITY ANALYSIS:**
{violence_description or 'Violence detected by advanced AI monitoring system'}

📹 **Evidence:** Video footage will be sent separately
🔗 **Live Feed:** {WEBSITE_URL}

⚡ **AUTOMATED RESPONSE ACTIVATED**
• Security protocols engaged
• Evidence being processed
• Authorities may be notified

🚨 **ACTION REQUIRED:**
Please verify the situation and take appropriate action immediately.

---
🛡️ Vision Guard AI Security System
Protecting What Matters Most"""

            # Send the text message first
            text_success = await self._send_telegram_message(chat_id, message)

            # Send video if available
            video_success = True
            if clip_info and 'clip_filename' in clip_info:
                video_path = os.path.join(BASE_DIR, 'recordings', clip_info['clip_filename'])
                if os.path.exists(video_path):
                    video_caption = f"📹 Violence Detection Video Evidence\n📍 {location_name}\n🕐 {formatted_time}"
                    video_success = await self._send_telegram_video(chat_id, video_path, video_caption)
                    if video_success:
                        logger.info(f"Video evidence sent to {phone_number}")
                    else:
                        logger.warning(f"Failed to send video to {phone_number}")
                else:
                    logger.warning(f"Video file not found: {video_path}")

            success = text_success  # Video is optional, text message is required
            if success:
                logger.info(f"Emergency Telegram alert sent successfully to {phone_number} (video: {video_success})")
                return True
            else:
                logger.error(f"Failed to send Telegram alert to {phone_number}")
                return False

        except Exception as e:
            logger.error(f"Error sending Telegram emergency alert: {str(e)}")
            return False

    async def _send_telegram_message(self, chat_id, message):
        """Send a message to Telegram using the bot API"""
        try:
            # Use current bot token from session
            bot_token = self.get_current_bot_token()
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            payload = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Telegram API error: {response.status} - {error_text}")
                        return False

        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return False

    async def _send_telegram_video(self, chat_id, video_path, caption=""):
        """Send a video file to Telegram using the bot API"""
        try:
            # Use current bot token from session
            bot_token = self.get_current_bot_token()
            url = f"https://api.telegram.org/bot{bot_token}/sendVideo"

            # Check if video file exists and is not too large (50MB limit for Telegram)
            if not os.path.exists(video_path):
                logger.error(f"Video file not found: {video_path}")
                return False

            file_size = os.path.getsize(video_path)
            if file_size > 50 * 1024 * 1024:  # 50MB limit
                logger.warning(f"Video file too large for Telegram: {file_size} bytes")
                return False

            async with aiohttp.ClientSession() as session:
                with open(video_path, 'rb') as video_file:
                    form_data = aiohttp.FormData()
                    form_data.add_field('chat_id', str(chat_id))
                    form_data.add_field('video', video_file, filename=os.path.basename(video_path))
                    if caption:
                        form_data.add_field('caption', caption)

                    async with session.post(url, data=form_data) as response:
                        if response.status == 200:
                            logger.info(f"Video sent successfully to chat_id: {chat_id}")
                            return True
                        else:
                            error_text = await response.text()
                            logger.error(f"Telegram video API error: {response.status} - {error_text}")
                            return False

        except Exception as e:
            logger.error(f"Error sending Telegram video: {str(e)}")
            return False

# Initialize Telegram service
telegram_service = TelegramNotificationService()

def send_telegram_alert_sync(violence_description, clip_info=None):
    """Synchronous wrapper for sending Telegram alerts"""
    try:
        # Create new event loop for this thread if needed
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Run the async function
        return loop.run_until_complete(
            telegram_service.send_emergency_alert(violence_description, clip_info)
        )
    except Exception as e:
        logger.error(f"Error in sync Telegram alert wrapper: {str(e)}")
        return False

def initialize_models():
    """Initialize YOLO and CNN+LSTM models"""
    global yolo_model, cnn_lstm_model, mp_pose, pose

    try:
        # Initialize YOLO model if not already loaded
        if yolo_model is None:
            logger.info(f"Loading YOLO model from {YOLO_MODEL_PATH}...")
            yolo_model = YOLO(YOLO_MODEL_PATH)
            logger.info(f"YOLO model loaded successfully with classes: {yolo_model.names}")

        # Initialize CNN+LSTM model if not already loaded
        if cnn_lstm_model is None:
            logger.info(f"Loading CNN+LSTM model from {CNN_LSTM_MODEL_PATH}...")
            cnn_lstm_model = tf.keras.models.load_model(CNN_LSTM_MODEL_PATH)
            logger.info("CNN+LSTM model loaded successfully")

        # Initialize MediaPipe Pose if not already initialized
        if mp_pose is None:
            mp_pose = mp.solutions.pose
            pose = mp_pose.Pose(static_image_mode=True)
            logger.info("MediaPipe Pose initialized successfully")

        return True
    except Exception as e:
        logger.error(f"Error initializing models: {str(e)}")
        return False

def process_frame(frame):
    """Apply YOLO detection, pose estimation, and violence detection on frame with frame queue"""
    global frame_queue, frame_timestamps, violence_detected_in_buffer, current_clip_detections
    global most_violent_frame_in_buffer, highest_violence_confidence_in_buffer, yolo_model, cnn_lstm_model, pose

    try:
        current_time = time.time()
        frame_copy = frame.copy()

        # Add frame to the sliding window buffer
        frame_queue.append(frame_copy)
        frame_timestamps.append(current_time)

        # Initialize frame-level violence detection
        frame_has_violence = False
        frame_detections = []

        # Perform YOLO detection
        if yolo_model is not None:
            results = yolo_model(frame)
            boxes = results[0].boxes.xyxy if len(results[0].boxes) > 0 else []

            # Process each detected bounding box
            for box in boxes:
                x1, y1, x2, y2 = map(int, box)

                # Crop the detected region for pose detection
                if y2 > y1 and x2 > x1:
                    cropped_image = frame[y1:y2, x1:x2]
                    img_rgb = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB)

                    # Apply MediaPipe pose estimation
                    if pose is not None:
                        pose_results = pose.process(img_rgb)

                        if pose_results.pose_landmarks:
                            # Extract keypoints
                            keypoints = []
                            for lm in pose_results.pose_landmarks.landmark:
                                keypoints.extend([lm.x, lm.y, lm.z, lm.visibility])

                            # Reshape for CNN+LSTM model
                            keypoints = np.array(keypoints).reshape(1, 33, 4)

                            # Make violence prediction
                            if cnn_lstm_model is not None:
                                prediction = cnn_lstm_model.predict(keypoints, verbose=0)
                                is_violence = prediction[0] > VIOLENCE_THRESHOLD
                                confidence = float(prediction[0])

                                if is_violence:
                                    frame_has_violence = True

                                    # Check if this is the most violent frame in buffer
                                    if confidence > highest_violence_confidence_in_buffer:
                                        highest_violence_confidence_in_buffer = confidence
                                        most_violent_frame_in_buffer = frame_copy.copy()

                                # Set color based on classification
                                color = (0, 0, 255) if is_violence else (0, 255, 0)
                                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                                # Create detection record
                                detection = {
                                    "timestamp": format_timestamp(current_time),
                                    "class": f"Violence: {is_violence}",
                                    "is_violence": bool(is_violence),
                                    "confidence": round(confidence if is_violence else 1.0 - confidence, 2),
                                    "bbox": {
                                        "x": float(x1) / frame.shape[1],
                                        "y": float(y1) / frame.shape[0],
                                        "width": float(x2 - x1) / frame.shape[1],
                                        "height": float(y2 - y1) / frame.shape[0]
                                    }
                                }
                                frame_detections.append(detection)

        # Add frame detections to current clip detections
        current_clip_detections.extend(frame_detections)

        # Check if violence was detected in this frame
        if frame_has_violence:
            violence_detected_in_buffer = True

            # Save the violence clip from buffer
            clip_info = save_violence_clip_from_buffer()
            if clip_info:
                logger.info(f"Violence detected! Saved clip: {clip_info['clip_filename']}")

                # Reset buffer-specific variables for next detection
                violence_detected_in_buffer = False
                highest_violence_confidence_in_buffer = 0.0
                most_violent_frame_in_buffer = None

        # Add overall violence status to frame (like upload workflow)
        violence_status = "Violence: True" if frame_has_violence else "Violence: False"
        text_color = (0, 0, 255) if frame_has_violence else (0, 255, 0)

        # Create background for text
        text_size = cv2.getTextSize(violence_status, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)[0]
        cv2.rectangle(frame, (10, 10), (10 + text_size[0], 10 + text_size[1] + 10), text_color, -1)

        # Add label to upper left corner
        cv2.putText(frame, violence_status, (15, 35),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)

        return frame

    except Exception as e:
        logger.error(f"Error processing frame: {str(e)}")
        return frame

def start_auto_recording():
    """Start automatic recording when violence is detected"""
    global camera, video_writer, is_recording

    try:
        # Create recordings directory if it doesn't exist
        recordings_dir = os.path.join(BASE_DIR, 'recordings')
        os.makedirs(recordings_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        video_filename = f'violence_detected_{timestamp}.mp4'
        video_filepath = os.path.join(recordings_dir, video_filename)

        # Get video properties
        width = int(camera.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = 10  # Set to 10 FPS as requested

        # Initialize video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(video_filepath, fourcc, fps, (width, height))
        is_recording = True

        logger.info(f"Auto-recording started: {video_filename}")

    except Exception as e:
        logger.error(f"Error starting auto-recording: {str(e)}")

def stop_auto_recording():
    """Stop automatic recording"""
    global video_writer, is_recording, auto_recording_start_time

    try:
        if video_writer is not None:
            video_writer.release()
            video_writer = None

        is_recording = False
        auto_recording_start_time = None
        logger.info("Auto-recording stopped")

    except Exception as e:
        logger.error(f"Error stopping auto-recording: {str(e)}")

def save_violence_clip_from_buffer():
    """Save a violence clip from the frame buffer when violence is detected"""
    global frame_queue, frame_timestamps, current_clip_detections, most_violent_frame_in_buffer

    try:
        if len(frame_queue) == 0:
            logger.warning("No frames in buffer to save")
            return None

        # Generate timestamp and filename
        timestamp = int(time.time())
        clip_filename = f"violence_clip_{timestamp}.mp4"
        clip_path = os.path.join(REALTIME_OUTPUT_FOLDER, clip_filename)
        json_filename = f"detections_{timestamp}_realtime.json"
        json_path = os.path.join(REALTIME_OUTPUT_FOLDER, json_filename)

        # Get frame dimensions from the first frame
        first_frame = frame_queue[0]
        height, width = first_frame.shape[:2]

        # Create video writer for the clip
        fourcc = cv2.VideoWriter_fourcc(*'avc1')
        clip_writer = cv2.VideoWriter(clip_path, fourcc, 10, (width, height))

        if not clip_writer.isOpened():
            # Try fallback codec
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            clip_writer = cv2.VideoWriter(clip_path, fourcc, 10, (width, height))

        if not clip_writer.isOpened():
            logger.error(f"Failed to create video writer for clip: {clip_path}")
            return None

        # Write all frames from buffer to the clip
        for frame in frame_queue:
            clip_writer.write(frame)

        clip_writer.release()

        # Create JSON metadata
        detections_data = {
            "filename": clip_filename,
            "source": "realtime",
            "timestamp": timestamp,
            "detections": current_clip_detections.copy(),
            "is_violent": True,
            "violence_frame_count": len([d for d in current_clip_detections if d.get('is_violence', False)]),
            "non_violence_frame_count": len([d for d in current_clip_detections if not d.get('is_violence', True)]),
            "buffer_duration_seconds": 6.0
        }

        # Get violence description from Ollama if we have the most violent frame
        violence_description = None
        if most_violent_frame_in_buffer is not None:
            logger.info("Getting violence description from Ollama for real-time clip...")
            violence_description = get_violence_description_from_ollama(most_violent_frame_in_buffer)
            detections_data["violence_description"] = violence_description
            logger.info(f"Ollama description for real-time clip: {violence_description}")

            # Send Telegram emergency alert
            logger.info("Sending Telegram emergency alert...")

            # Check if user has Telegram connected
            phone_number = current_user_session.get('phone_number')
            if phone_number:
                logger.info(f"Found user phone number: {phone_number}")

                # Check if phone is mapped to chat ID
                if phone_number in phone_to_chat_mapping:
                    logger.info(f"Phone {phone_number} is mapped to chat_id: {phone_to_chat_mapping[phone_number]}")
                else:
                    logger.warning(f"Phone {phone_number} is not mapped to any chat_id. Attempting auto-discovery...")
                    # Try to auto-discover chat ID
                    chat_id = telegram_service.auto_discover_chat_id(phone_number)
                    if chat_id:
                        logger.info(f"Auto-discovered chat_id {chat_id} for phone {phone_number}")
                    else:
                        logger.warning(f"Could not auto-discover chat_id for phone {phone_number}")
            else:
                logger.warning("No phone number found in current user session")

            telegram_success = send_telegram_alert_sync(violence_description, {
                'clip_filename': clip_filename,
                'timestamp': timestamp
            })
            if telegram_success:
                logger.info("Telegram emergency alert sent successfully")
                detections_data["telegram_alert_sent"] = True
            else:
                logger.warning("Failed to send Telegram emergency alert")
                detections_data["telegram_alert_sent"] = False

                # Log detailed failure information
                if phone_number:
                    if phone_number in phone_to_chat_mapping:
                        logger.error(f"Telegram alert failed despite having mapping: {phone_number} -> {phone_to_chat_mapping[phone_number]}")
                    else:
                        logger.error(f"Telegram alert failed: No chat_id mapping for phone {phone_number}")
                        logger.info("User needs to connect Telegram by starting the bot: https://t.me/Visionguard_security_bot")
                else:
                    logger.error("Telegram alert failed: No user session found")

        # Save JSON metadata
        with open(json_path, 'w') as f:
            json.dump(detections_data, f, indent=2)

        logger.info(f"Violence clip saved: {clip_filename}")

        # Reset clip-specific variables
        current_clip_detections.clear()

        return {
            'clip_filename': clip_filename,
            'clip_path': f'/output_videos/realtime/{clip_filename}',
            'json_path': f'/output_videos/realtime/{json_filename}',
            'detections': detections_data
        }

    except Exception as e:
        logger.error(f"Error saving violence clip from buffer: {str(e)}")
        return None

def generate_frames():
    """Generate frames for video stream"""
    global camera, output_frame, lock, last_detection_time, yolo_model, cnn_lstm_model

    try:
        # Initialize models
        if yolo_model is None or cnn_lstm_model is None:
            logger.info("Initializing models in generate_frames...")
            if not initialize_models():
                logger.error("Failed to initialize models in generate_frames")
                # Return a default error frame
                error_frame = create_error_frame("Failed to initialize models")
                ret, buffer = cv2.imencode('.jpg', error_frame)
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
                return

        # Create a placeholder frame
        placeholder_frame = create_error_frame("Starting camera...")
        ret, buffer = cv2.imencode('.jpg', placeholder_frame)
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')

        while True:
            if camera is None or not camera.isOpened():
                logger.warning("Camera not available in generate_frames")
                error_frame = create_error_frame("Camera not available")
                ret, buffer = cv2.imencode('.jpg', error_frame)
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
                time.sleep(1)  # Wait before trying again
                continue

            try:
                success, frame = camera.read()
                if not success:
                    logger.warning("Failed to read frame from camera")
                    error_frame = create_error_frame("Failed to read frame")
                    ret, buffer = cv2.imencode('.jpg', error_frame)
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
                    continue

                current_time = time.time()

                # Process frame with YOLO if enough time has passed (10 FPS)
                if current_time - last_detection_time >= DETECTION_INTERVAL:
                    processed_frame = process_frame(frame.copy())
                    last_detection_time = current_time

                    # Save the processed frame
                    with lock:
                        output_frame = processed_frame
                        if is_recording and video_writer is not None:
                            video_writer.write(processed_frame)
                else:
                    with lock:
                        output_frame = frame
                        if is_recording and video_writer is not None:
                            video_writer.write(frame)

                # Encode the frame for streaming
                ret, buffer = cv2.imencode('.jpg', output_frame)
                if not ret:
                    logger.warning("Failed to encode frame")
                    continue

                # Yield the frame in bytes
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
            except Exception as e:
                logger.error(f"Error in frame processing loop: {str(e)}")
                error_frame = create_error_frame(f"Error: {str(e)}")
                ret, buffer = cv2.imencode('.jpg', error_frame)
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
                time.sleep(1)  # Wait before trying again
    except Exception as e:
        logger.error(f"Error in generate_frames: {str(e)}")
        # Return a default error frame
        error_frame = create_error_frame(f"Error: {str(e)}")
        ret, buffer = cv2.imencode('.jpg', error_frame)
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')

def create_error_frame(error_message):
    """Create an error frame with the given message"""
    # Create a black frame
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    # Add error message
    cv2.putText(frame, error_message, (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    return frame

@app.route('/start_camera', methods=['POST'])
def start_camera():
    """Start the camera feed with violence detection and auto-recording"""
    global camera, yolo_model, cnn_lstm_model, is_recording, video_writer, frame_queue, frame_timestamps
    global violence_detected_in_buffer, current_clip_detections, most_violent_frame_in_buffer, highest_violence_confidence_in_buffer

    try:
        # Initialize models first
        logger.info("Initializing models for real-time detection...")
        if not initialize_models():
            logger.error("Failed to initialize models in start_camera")
            return jsonify({'status': 'error', 'message': 'Failed to initialize models'}), 500

        # Release the camera if it's already open
        if camera is not None:
            logger.info("Releasing existing camera...")
            camera.release()
            camera = None

        # Reset frame buffer and detection variables
        frame_queue.clear()
        frame_timestamps.clear()
        current_clip_detections.clear()
        violence_detected_in_buffer = False
        most_violent_frame_in_buffer = None
        highest_violence_confidence_in_buffer = 0.0

        # Open the webcam
        logger.info("Opening webcam...")
        camera = cv2.VideoCapture(0)  # Use default webcam

        # Check if camera opened successfully
        if not camera.isOpened():
            logger.error("Could not open webcam")
            return jsonify({'status': 'error', 'message': 'Could not open webcam. Please check your camera connection.'}), 500

        logger.info("Webcam opened successfully")
        logger.info(f"Camera properties: Width={camera.get(cv2.CAP_PROP_FRAME_WIDTH)}, Height={camera.get(cv2.CAP_PROP_FRAME_HEIGHT)}")

        # Test reading a frame
        ret, frame = camera.read()
        if not ret:
            logger.error("Could not read frame from webcam")
            camera.release()
            camera = None
            return jsonify({'status': 'error', 'message': 'Could not read frame from webcam'}), 500

        logger.info(f"Successfully read frame with shape: {frame.shape}")

        # Auto-start recording when camera opens (as requested)
        if auto_recording_enabled and not is_recording:
            logger.info("Auto-starting recording as requested...")
            try:
                # Create recordings directory if it doesn't exist
                recordings_dir = os.path.join(BASE_DIR, 'recordings')
                os.makedirs(recordings_dir, exist_ok=True)

                # Generate filename with timestamp
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                video_filename = f'realtime_recording_{timestamp}.mp4'
                video_filepath = os.path.join(recordings_dir, video_filename)

                # Get video properties
                width = int(camera.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = 10  # Set to 10 FPS as requested

                # Initialize video writer
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                video_writer = cv2.VideoWriter(video_filepath, fourcc, fps, (width, height))
                is_recording = True

                logger.info(f"Auto-recording started: {video_filename}")
            except Exception as e:
                logger.warning(f"Failed to start auto-recording: {str(e)}")

        return jsonify({
            'status': 'success',
            'message': 'Camera started with violence detection and auto-recording',
            'auto_recording': is_recording
        }), 200
    except Exception as e:
        logger.error(f"Error starting camera: {str(e)}")
        # Clean up if there was an error
        if camera is not None:
            try:
                camera.release()
                camera = None
            except:
                pass
        return jsonify({'status': 'error', 'message': f'Error starting camera: {str(e)}'}), 500

@app.route('/stop_camera', methods=['POST'])
def stop_camera():
    """Stop the camera feed"""
    global camera, is_recording, video_writer

    try:
        if is_recording:
            video_writer.release()
            is_recording = False
            video_writer = None

        if camera is not None:
            camera.release()
            camera = None

        return jsonify({'status': 'success', 'message': 'Camera stopped'}), 200
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/start_recording', methods=['POST'])
def start_recording():
    """Start recording the camera feed"""
    global camera, video_writer, is_recording

    try:
        if camera is None or not camera.isOpened():
            return jsonify({'status': 'error', 'message': 'Camera not started'}), 400

        if is_recording:
            return jsonify({'status': 'error', 'message': 'Already recording'}), 400

        # Create recordings directory if it doesn't exist
        recordings_dir = os.path.join(BASE_DIR, 'recordings')
        os.makedirs(recordings_dir, exist_ok=True)

        # Create detections directory for JSON files
        detections_dir = os.path.join(recordings_dir, 'detections')
        os.makedirs(detections_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        video_filename = f'recording_{timestamp}.mp4'
        video_filepath = os.path.join(recordings_dir, video_filename)

        # Get video properties
        width = int(camera.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = 10  # Set to 10 FPS as requested

        # Initialize video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(video_filepath, fourcc, fps, (width, height))
        is_recording = True

        return jsonify({
            'status': 'success',
            'message': 'Recording started',
            'filename': video_filename
        }), 200
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/stop_recording', methods=['POST'])
def stop_recording():
    """Stop recording the camera feed"""
    global video_writer, is_recording

    try:
        if not is_recording:
            return jsonify({'status': 'error', 'message': 'Not recording'}), 400

        video_writer.release()
        video_writer = None
        is_recording = False

        return jsonify({
            'status': 'success',
            'message': 'Recording stopped'
        }), 200
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/video_feed')
def video_feed():
    """Video streaming route for real-time violence detection"""
    response = Response(generate_frames(),
                   mimetype='multipart/x-mixed-replace; boundary=frame')
    response.headers['Access-Control-Allow-Origin'] = '*'
    return response

@app.route('/camera_status', methods=['GET'])
def camera_status():
    """Check if camera is running"""
    global camera
    try:
        if camera is not None and camera.isOpened():
            return jsonify({'status': 'running', 'auto_recording': is_recording}), 200
        else:
            return jsonify({'status': 'stopped', 'auto_recording': False}), 200
    except Exception as e:
        logger.error(f"Error checking camera status: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/realtime_clips', methods=['GET'])
def get_realtime_clips():
    """Get list of real-time violence clips"""
    try:
        clips = []
        if os.path.exists(REALTIME_OUTPUT_FOLDER):
            # Get all video files in the realtime output folder
            for filename in os.listdir(REALTIME_OUTPUT_FOLDER):
                if filename.endswith('.mp4') and filename.startswith('violence_clip_'):
                    # Extract timestamp from filename
                    timestamp_str = filename.replace('violence_clip_', '').replace('.mp4', '')
                    try:
                        timestamp = int(timestamp_str)

                        # Check for corresponding JSON file
                        json_filename = f"detections_{timestamp_str}_realtime.json"
                        json_path = os.path.join(REALTIME_OUTPUT_FOLDER, json_filename)

                        clip_info = {
                            'filename': filename,
                            'timestamp': timestamp,
                            'video_url': f'/output_videos/realtime/{filename}',
                            'created_at': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        }

                        # Add JSON data if available
                        if os.path.exists(json_path):
                            with open(json_path, 'r') as f:
                                json_data = json.load(f)
                                clip_info['detections'] = json_data.get('detections', [])
                                clip_info['violence_description'] = json_data.get('violence_description')
                                clip_info['json_url'] = f'/output_videos/realtime/{json_filename}'

                        clips.append(clip_info)
                    except ValueError:
                        continue  # Skip files with invalid timestamp format

            # Sort clips by timestamp (newest first)
            clips.sort(key=lambda x: x['timestamp'], reverse=True)

        return jsonify({
            'status': 'success',
            'clips': clips,
            'total_clips': len(clips)
        }), 200

    except Exception as e:
        logger.error(f"Error getting real-time clips: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/set_user_session', methods=['POST'])
def set_user_session():
    """Set user session data for Telegram notifications"""
    global current_user_session
    try:
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'No data provided'}), 400

        # Update session with provided data
        if 'phone_number' in data:
            current_user_session['phone_number'] = data['phone_number']
        if 'telegram_chat_id' in data:
            current_user_session['telegram_chat_id'] = data['telegram_chat_id']
        if 'full_name' in data:
            current_user_session['full_name'] = data['full_name']
        if 'email' in data:
            current_user_session['email'] = data['email']
        if 'location_name' in data:
            current_user_session['location_name'] = data['location_name']
        if 'bot_token' in data:
            current_user_session['bot_token'] = data['bot_token']

        logger.info(f"User session updated: {current_user_session}")

        return jsonify({
            'status': 'success',
            'message': 'User session updated successfully',
            'session': {k: v for k, v in current_user_session.items() if k != 'telegram_chat_id'}  # Don't expose chat_id
        }), 200

    except Exception as e:
        logger.error(f"Error setting user session: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/get_user_session', methods=['GET'])
def get_user_session():
    """Get current user session data"""
    global current_user_session
    try:
        return jsonify({
            'status': 'success',
            'session': {k: v for k, v in current_user_session.items() if k != 'telegram_chat_id'}  # Don't expose chat_id
        }), 200

    except Exception as e:
        logger.error(f"Error getting user session: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/test_telegram', methods=['POST'])
def test_telegram():
    """Test Telegram notification system"""
    global current_user_session, phone_to_chat_mapping
    try:
        data = request.get_json() or {}
        test_message = data.get('message', '🚨 TEST ALERT: This is a test message from Vision Guard security system.')

        # Check current user session
        phone_number = current_user_session.get('phone_number')
        logger.info(f"Testing Telegram for phone: {phone_number}")
        logger.info(f"Current session: {current_user_session}")
        logger.info(f"Phone mappings: {phone_to_chat_mapping}")

        if not phone_number:
            return jsonify({
                'status': 'error',
                'message': 'No user session found. Please login first.',
                'debug_info': {
                    'session_keys': list(current_user_session.keys()),
                    'phone_mappings': len(phone_to_chat_mapping)
                }
            }), 400

        # Check if phone is connected to Telegram
        chat_id = phone_to_chat_mapping.get(phone_number)
        logger.info(f"Found chat_id for {phone_number}: {chat_id}")

        if not chat_id:
            # Try auto-discovery
            logger.info(f"Attempting auto-discovery for phone {phone_number}")
            chat_id = telegram_service.auto_discover_chat_id(phone_number)
            logger.info(f"Auto-discovery result: {chat_id}")

            # If auto-discovery found a chat_id, save it
            if chat_id:
                phone_to_chat_mapping[phone_number] = chat_id
                current_user_session['telegram_chat_id'] = chat_id
                logger.info(f"Saved auto-discovered mapping: {phone_number} -> {chat_id}")

        if not chat_id:
            return jsonify({
                'status': 'error',
                'message': f'Phone {phone_number} is not connected to Telegram. Please start the bot first.',
                'bot_link': 'https://t.me/Visionguard_security_bot',
                'debug_info': {
                    'phone_number': phone_number,
                    'available_mappings': list(phone_to_chat_mapping.keys()),
                    'recent_bot_activity': 'Check if you started the bot recently'
                }
            }), 400

        # Send test alert using the direct send function
        logger.info(f"Sending test message to chat_id: {chat_id}")
        success = send_telegram_message_sync(chat_id, test_message)
        logger.info(f"Message send result: {success}")

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Test Telegram message sent successfully',
                'debug_info': {
                    'phone_number': phone_number,
                    'chat_id': chat_id,
                    'message_sent': test_message
                }
            }), 200
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to send test Telegram message',
                'debug_info': {
                    'phone_number': phone_number,
                    'chat_id': chat_id,
                    'bot_token_available': bool(TELEGRAM_BOT_TOKEN)
                }
            }), 500

    except Exception as e:
        logger.error(f"Error testing Telegram: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/get_telegram_updates', methods=['GET'])
def get_telegram_updates():
    """Get recent Telegram updates to find Chat ID"""
    try:
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"

        response = requests.get(url)
        data = response.json()

        if response.status_code == 200 and data.get('ok'):
            updates = data.get('result', [])
            chat_ids = []

            for update in updates:
                if 'message' in update:
                    chat_id = update['message']['chat']['id']
                    username = update['message']['chat'].get('username', 'N/A')
                    first_name = update['message']['chat'].get('first_name', 'N/A')
                    chat_ids.append({
                        'chat_id': chat_id,
                        'username': username,
                        'first_name': first_name,
                        'message': update['message'].get('text', 'N/A')
                    })

            return jsonify({
                'status': 'success',
                'updates': chat_ids,
                'message': 'Send a message to your bot first if no updates are shown'
            }), 200
        else:
            return jsonify({
                'status': 'error',
                'message': f"Telegram API error: {data.get('description', 'Unknown error')}"
            }), 500

    except Exception as e:
        logger.error(f"Error getting Telegram updates: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

def create_automatic_telegram_connection(phone_number, full_name):
    """Create automatic Telegram connection for new user"""
    global phone_to_chat_mapping

    try:
        # Method 1: Check if user has already interacted with the bot
        chat_id = find_telegram_chat_by_recent_activity(phone_number)

        if chat_id:
            phone_to_chat_mapping[phone_number] = chat_id
            logger.info(f"Found existing Telegram connection for {phone_number}")
            return chat_id

        # Method 2: Create a temporary mapping that will be activated when user starts the bot
        # Generate a simple verification code based on phone number
        verification_code = generate_phone_verification_code(phone_number)

        # Store the mapping for when user interacts with bot
        verification_codes[verification_code] = {
            'phone': phone_number,
            'full_name': full_name,
            'timestamp': time.time(),
            'auto_generated': True
        }

        logger.info(f"Created automatic verification code {verification_code} for {phone_number}")
        return None  # Will be connected when user starts bot

    except Exception as e:
        logger.error(f"Error creating automatic Telegram connection: {str(e)}")
        return None

def find_telegram_chat_by_recent_activity(phone_number):
    """Try to find Telegram chat ID by checking recent bot activity"""
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates"
        response = requests.get(url)
        data = response.json()

        if response.status_code == 200 and data.get('ok'):
            updates = data.get('result', [])

            # Look for recent messages from users
            for update in reversed(updates[-10:]):  # Check last 10 updates
                if 'message' in update:
                    chat = update['message']['chat']
                    chat_id = chat['id']

                    # Check if this could be our user (basic heuristics)
                    # In production, you'd use proper phone number verification
                    if 'contact' in update['message']:
                        # User shared contact - could match phone number
                        contact_phone = update['message']['contact'].get('phone_number', '')
                        if contact_phone.endswith(phone_number[-7:]):  # Match last 7 digits
                            return chat_id

        return None

    except Exception as e:
        logger.error(f"Error finding Telegram chat by recent activity: {str(e)}")
        return None

def generate_phone_verification_code(phone_number):
    """Generate verification code based on phone number for automatic connection"""
    import hashlib
    # Create a deterministic but secure code based on phone number
    hash_input = f"{phone_number}_{int(time.time() // 3600)}"  # Changes every hour
    hash_object = hashlib.md5(hash_input.encode())
    return hash_object.hexdigest()[:6].upper()

def send_automatic_telegram_welcome(phone_number, full_name):
    """Automatically send welcome message to user's Telegram"""
    global phone_to_chat_mapping

    try:
        # Try to find existing chat ID for this phone
        chat_id = phone_to_chat_mapping.get(phone_number)

        if not chat_id:
            # Try to auto-discover chat ID
            chat_id = find_telegram_chat_by_phone(phone_number)
            if chat_id:
                phone_to_chat_mapping[phone_number] = chat_id

        if chat_id:
            welcome_message = f"""🛡️ **VISION GUARD SECURITY ACTIVATED** 🛡️

Hello {full_name}! 👋

✅ **Your account is now automatically connected!**

You will receive instant notifications when violence is detected:
• 🚨 Real-time alerts with AI analysis
• 📹 Video evidence of incidents
• 📍 Location and timestamp information
• 📊 Detailed security reports

🔗 **Your security monitoring is now active!**

---
Vision Guard - Protecting What Matters Most 🛡️"""

            success = send_telegram_message_sync(chat_id, welcome_message)
            if success:
                logger.info(f"Automatic welcome message sent to {phone_number}")
                return True

        return False

    except Exception as e:
        logger.error(f"Error sending automatic welcome: {str(e)}")
        return False

@app.route('/register_user', methods=['POST'])
def register_user():
    """Register a new user with automatic Telegram connection"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'No data provided'}), 400

        # Extract user information
        full_name = data.get('fullName', '')
        email = data.get('email', '')
        phone = data.get('phone', '')
        password = data.get('password', '')

        if not all([full_name, email, phone, password]):
            return jsonify({'status': 'error', 'message': 'All fields are required'}), 400

        # Set user session for immediate use
        global current_user_session
        current_user_session.update({
            'phone_number': phone,
            'full_name': full_name,
            'email': email,
            'location_name': 'Security Camera Location'
        })

        # Create automatic Telegram connection
        chat_id = create_automatic_telegram_connection(phone_number=phone, full_name=full_name)
        telegram_connected = chat_id is not None

        # If we found an existing connection, send welcome message
        if telegram_connected:
            welcome_sent = send_automatic_telegram_welcome(phone_number=phone, full_name=full_name)
            logger.info(f"User registered: {full_name} ({email}) - Telegram connected and welcome sent: {welcome_sent}")
        else:
            logger.info(f"User registered: {full_name} ({email}) - Telegram will connect when user starts bot")

        return jsonify({
            'status': 'success',
            'message': 'Registration successful! Vision Guard will automatically send you violence detection alerts via Telegram.',
            'telegram_connected': telegram_connected,
            'auto_connect_info': 'Telegram notifications will activate automatically when violence is detected' if not telegram_connected else 'Telegram notifications are active',
            'user': {
                'fullName': full_name,
                'email': email,
                'phone': phone
            }
        }), 200

    except Exception as e:
        logger.error(f"Error registering user: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/map_phone_to_chat', methods=['POST'])
def map_phone_to_chat():
    """Manually map a phone number to a Telegram chat ID"""
    global phone_to_chat_mapping
    try:
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'No data provided'}), 400

        phone_number = data.get('phone_number')
        chat_id = data.get('chat_id')

        if not phone_number or not chat_id:
            return jsonify({'status': 'error', 'message': 'Phone number and chat ID are required'}), 400

        # Store the mapping
        phone_to_chat_mapping[phone_number] = chat_id

        # Update current session if it matches
        global current_user_session
        if current_user_session.get('phone_number') == phone_number:
            current_user_session['telegram_chat_id'] = chat_id

        logger.info(f"Mapped phone {phone_number} to chat_id {chat_id}")

        return jsonify({
            'status': 'success',
            'message': f'Phone number {phone_number} mapped to chat ID {chat_id}',
            'mapping': {phone_number: chat_id}
        }), 200

    except Exception as e:
        logger.error(f"Error mapping phone to chat: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/telegram_webhook', methods=['POST'])
def telegram_webhook():
    """Handle incoming Telegram webhook updates"""
    global phone_to_chat_mapping, verification_codes
    try:
        update = request.get_json()

        if not update or 'message' not in update:
            return jsonify({'status': 'ok'}), 200

        message = update['message']
        chat_id = message['chat']['id']
        text = message.get('text', '').strip()

        logger.info(f"Received Telegram message: {text} from chat_id: {chat_id}")

        # Handle /start command
        if text.startswith('/start'):
            # Extract verification code if provided
            parts = text.split()
            verification_code = parts[1] if len(parts) > 1 else None

            # Try to find user by verification code
            user_found = False
            if verification_code and verification_code in verification_codes:
                phone_number = verification_codes[verification_code]['phone']
                full_name = verification_codes[verification_code]['full_name']

                # Map phone to chat ID
                phone_to_chat_mapping[phone_number] = chat_id

                # Remove used verification code
                del verification_codes[verification_code]

                # Send confirmation message
                welcome_message = f"""🛡️ **VISION GUARD SECURITY ACTIVATED** 🛡️

Hello {full_name}! ✅

**Your account is now automatically connected!**

📱 **Instant Notifications Active:**
• 🚨 Real-time violence detection alerts
• 📹 Video evidence of incidents
• 📍 Location and timestamp information
• 🤖 AI-generated security reports

🔗 **Live Feed Access:** {WEBSITE_URL}

🚨 **Your security monitoring is now fully operational!**
You will receive immediate alerts when violence is detected.

---
🛡️ Vision Guard - Protecting What Matters Most"""

                send_telegram_message_sync(chat_id, welcome_message)
                logger.info(f"Successfully auto-linked phone {phone_number} to chat_id {chat_id}")
                user_found = True

            # If no specific verification code, try to auto-connect any recent registrations
            if not user_found:
                # Check for recent registrations that might match this user
                recent_registrations = [code for code, data in verification_codes.items()
                                      if data.get('auto_generated') and
                                      time.time() - data['timestamp'] < 300]  # 5 minutes

                if recent_registrations:
                    # Use the most recent registration
                    latest_code = max(recent_registrations,
                                    key=lambda x: verification_codes[x]['timestamp'])
                    phone_number = verification_codes[latest_code]['phone']
                    full_name = verification_codes[latest_code]['full_name']

                    # Map phone to chat ID
                    phone_to_chat_mapping[phone_number] = chat_id

                    # Remove used verification code
                    del verification_codes[latest_code]

                    # Send welcome message
                    welcome_message = f"""🛡️ **VISION GUARD SECURITY ACTIVATED** 🛡️

Hello {full_name}! ✅

**Your account has been automatically connected!**

📱 **You will now receive instant alerts for:**
• Violence detection with video evidence
• Location and time information
• AI security analysis reports

🚨 **Your security monitoring is active!**

---
🛡️ Vision Guard - Protecting What Matters Most"""

                    send_telegram_message_sync(chat_id, welcome_message)
                    logger.info(f"Auto-connected recent registration {phone_number} to chat_id {chat_id}")
                    user_found = True

            # If still no user found, send general welcome
            if not user_found:
                help_message = """🛡️ **Welcome to Vision Guard Security!**

To activate automatic violence detection alerts:

1. **Register** on our website first
2. **Start monitoring** - alerts will be sent automatically
3. **No setup required** - just register and you're protected!

🚨 **Instant Notifications Include:**
• Real-time violence detection
• Video evidence
• Location and time stamps
• AI security reports

Visit our website to get started with Vision Guard Security."""

                send_telegram_message_sync(chat_id, help_message)

        return jsonify({'status': 'ok'}), 200

    except Exception as e:
        logger.error(f"Error handling Telegram webhook: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

def send_telegram_message_sync(chat_id, message):
    """Send a Telegram message synchronously"""
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'Markdown'
        }

        response = requests.post(url, json=payload)
        return response.status_code == 200

    except Exception as e:
        logger.error(f"Error sending Telegram message: {str(e)}")
        return False

def generate_verification_code():
    """Generate a unique 6-digit verification code"""
    import random
    import string
    return ''.join(random.choices(string.digits, k=6))

def cleanup_expired_verification_codes():
    """Clean up verification codes older than 1 hour"""
    global verification_codes
    current_time = time.time()
    expired_codes = []

    for code, data in verification_codes.items():
        if current_time - data['timestamp'] > 3600:  # 1 hour
            expired_codes.append(code)

    for code in expired_codes:
        del verification_codes[code]
        logger.info(f"Removed expired verification code: {code}")

@app.route('/check_telegram_status', methods=['POST'])
def check_telegram_status():
    """Check if user's phone number is linked to Telegram"""
    global phone_to_chat_mapping
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')

        if not phone_number:
            return jsonify({'status': 'error', 'message': 'Phone number required'}), 400

        is_linked = phone_number in phone_to_chat_mapping
        chat_id = phone_to_chat_mapping.get(phone_number) if is_linked else None

        return jsonify({
            'status': 'success',
            'is_linked': is_linked,
            'chat_id': chat_id,
            'message': 'Telegram linked' if is_linked else 'Telegram not linked'
        }), 200

    except Exception as e:
        logger.error(f"Error checking Telegram status: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/connect_telegram', methods=['POST'])
def connect_telegram():
    """Automatically connect user's phone to Telegram by checking recent bot activity"""
    global phone_to_chat_mapping, current_user_session
    try:
        data = request.get_json() or {}
        phone_number = data.get('phone_number') or current_user_session.get('phone_number')

        if not phone_number:
            return jsonify({'status': 'error', 'message': 'Phone number required'}), 400

        # Try to auto-discover chat ID
        chat_id = telegram_service.auto_discover_chat_id(phone_number)

        if chat_id:
            # Update session
            current_user_session['telegram_chat_id'] = chat_id

            # Send confirmation message
            welcome_message = f"""🛡️ **VISION GUARD CONNECTED** 🛡️

✅ **Your Telegram is now connected!**

You will receive instant alerts when violence is detected:
• 📹 Video evidence
• 📍 Location and timestamp
• 🤖 AI security analysis

🚨 **Your security monitoring is active!**

---
Vision Guard - Protecting What Matters Most 🛡️"""

            success = send_telegram_message_sync(chat_id, welcome_message)

            return jsonify({
                'status': 'success',
                'message': 'Telegram connected successfully!',
                'chat_id': chat_id,
                'welcome_sent': success
            }), 200
        else:
            # If auto-discovery failed, try to use the most recent chat ID from bot activity
            logger.info("Auto-discovery failed, trying manual fallback...")

            # Get recent updates and use the most recent chat ID
            try:
                url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates"
                response = requests.get(url)
                data = response.json()

                if response.status_code == 200 and data.get('ok'):
                    updates = data.get('result', [])
                    if updates:
                        # Use the most recent chat ID
                        latest_chat_id = updates[-1]['message']['chat']['id']
                        logger.info(f"Using latest chat_id as fallback: {latest_chat_id}")

                        # Map it to the phone number
                        phone_to_chat_mapping[phone_number] = latest_chat_id
                        current_user_session['telegram_chat_id'] = latest_chat_id

                        # Send test message
                        test_success = send_telegram_message_sync(latest_chat_id,
                            "🛡️ Vision Guard connected! If this is your account, you're all set for violence detection alerts.")

                        if test_success:
                            return jsonify({
                                'status': 'success',
                                'message': 'Telegram connected using recent activity!',
                                'chat_id': latest_chat_id,
                                'note': 'Connected using your recent bot interaction'
                            }), 200
            except Exception as fallback_error:
                logger.error(f"Fallback connection failed: {fallback_error}")

            return jsonify({
                'status': 'error',
                'message': 'Could not find your Telegram. Please start the bot first: https://t.me/Visionguard_security_bot',
                'bot_link': 'https://t.me/Visionguard_security_bot',
                'instructions': 'Click the bot link, tap START, then try connecting again'
            }), 400

    except Exception as e:
        logger.error(f"Error connecting Telegram: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/quick_connect_telegram', methods=['POST'])
def quick_connect_telegram():
    """Quick connect using the most recent Telegram chat ID"""
    global phone_to_chat_mapping, current_user_session
    try:
        data = request.get_json() or {}
        phone_number = data.get('phone_number') or current_user_session.get('phone_number')

        if not phone_number:
            return jsonify({'status': 'error', 'message': 'Phone number required'}), 400

        # Get the most recent chat ID from bot updates
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates"
        response = requests.get(url)
        data = response.json()

        if response.status_code == 200 and data.get('ok'):
            updates = data.get('result', [])
            if updates:
                # Use the most recent chat ID
                latest_chat_id = updates[-1]['message']['chat']['id']

                # Map it to the phone number
                phone_to_chat_mapping[phone_number] = latest_chat_id
                current_user_session['telegram_chat_id'] = latest_chat_id

                logger.info(f"Quick-connected phone {phone_number} to chat_id {latest_chat_id}")

                # Send confirmation message
                success = send_telegram_message_sync(latest_chat_id,
                    "🛡️ **VISION GUARD QUICK CONNECT** 🛡️\n\n✅ Your phone is now connected to receive violence detection alerts!")

                return jsonify({
                    'status': 'success',
                    'message': 'Quick connection successful!',
                    'chat_id': latest_chat_id,
                    'phone_number': phone_number
                }), 200
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'No recent bot activity found. Please start the bot first.',
                    'bot_link': 'https://t.me/Visionguard_security_bot'
                }), 400
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to get bot updates'
            }), 500

    except Exception as e:
        logger.error(f"Error in quick connect: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/setup_webhook', methods=['POST'])
def setup_webhook():
    """Set up Telegram webhook"""
    try:
        # Get the webhook URL from request or use default
        data = request.get_json() or {}
        webhook_url = data.get('webhook_url', 'https://your-domain.com/telegram_webhook')

        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/setWebhook"
        payload = {'url': webhook_url}

        response = requests.post(url, json=payload)
        result = response.json()

        if response.status_code == 200 and result.get('ok'):
            return jsonify({
                'status': 'success',
                'message': 'Webhook set up successfully',
                'webhook_url': webhook_url
            }), 200
        else:
            return jsonify({
                'status': 'error',
                'message': f"Failed to set webhook: {result.get('description', 'Unknown error')}"
            }), 500

    except Exception as e:
        logger.error(f"Error setting up webhook: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

# Add a simple root route for health checks
@app.route('/', methods=['GET'])
def root_endpoint():
    """Simple root endpoint"""
    return jsonify({
        'status': 'ok',
        'message': 'Vision Guard Backend is running',
        'version': '1.0.0'
    }), 200

# Update the main block to include cleanup
if __name__ == '__main__':
    try:
        logger.info("Starting Flask server...")
        app.run(host='0.0.0.0', port=5001, debug=True, threaded=True)
    finally:
        if camera is not None:
            camera.release()






