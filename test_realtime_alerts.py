#!/usr/bin/env python3
"""
Test Real-time Violence Detection Alerts
This script tests the complete real-time flow including session management
"""

import requests
import json
import time

# Configuration
BACKEND_URL = "http://localhost:5001"

def setup_complete_session():
    """Set up complete user session and Telegram connection"""
    print("🔧 Setting up complete session...")
    
    # Step 1: Set user session
    session_data = {
        "phone_number": "01001747852",
        "full_name": "Test User",
        "email": "<EMAIL>",
        "location_name": "Test Security Camera Location"
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/set_user_session", json=session_data)
        if response.status_code == 200:
            print("✅ User session set successfully")
        else:
            print(f"❌ Failed to set session: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Error setting session: {e}")
        return False
    
    # Step 2: Connect Telegram
    try:
        response = requests.post(f"{BACKEND_URL}/quick_connect_telegram", json={})
        data = response.json()
        
        if response.status_code == 200:
            print(f"✅ Telegram connected to chat ID: {data['chat_id']}")
            return True
        else:
            print(f"❌ Telegram connection failed: {data['message']}")
            return False
    except Exception as e:
        print(f"❌ Error connecting Telegram: {e}")
        return False

def check_session_status():
    """Check current session and mapping status"""
    print("🔍 Checking session status...")
    try:
        response = requests.get(f"{BACKEND_URL}/debug_session")
        data = response.json()
        
        if response.status_code == 200:
            session = data['current_user_session']
            phone_mapped = data['phone_is_mapped']
            
            print("📊 Current Status:")
            print(f"   📱 Phone: {session.get('phone_number', 'Not set')}")
            print(f"   👤 Name: {session.get('full_name', 'Not set')}")
            print(f"   💬 Telegram: {'Connected' if phone_mapped else 'Not connected'}")
            print(f"   📍 Location: {session.get('location_name', 'Not set')}")
            
            return session.get('phone_number') and phone_mapped
        else:
            print(f"❌ Failed to check status: {data}")
            return False
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return False

def test_camera_start():
    """Test starting the camera and check session status"""
    print("🎥 Testing camera start with session...")
    try:
        response = requests.post(f"{BACKEND_URL}/start_camera")
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Camera started successfully!")
            print(f"   Auto-recording: {data.get('auto_recording', False)}")
            print(f"   Alerts enabled: {data.get('alerts_enabled', False)}")
            print(f"   User phone: {data.get('user_phone', 'Not set')}")
            print(f"   Session status: {data.get('session_status', 'Unknown')}")
            print(f"   Telegram connected: {data.get('telegram_connected', False)}")
            
            return data.get('alerts_enabled', False)
        else:
            print(f"❌ Camera start failed: {data['message']}")
            return False
    except Exception as e:
        print(f"❌ Error starting camera: {e}")
        return False

def test_stop_camera():
    """Stop the camera"""
    print("🛑 Stopping camera...")
    try:
        response = requests.post(f"{BACKEND_URL}/stop_camera")
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Camera stopped successfully")
            return True
        else:
            print(f"❌ Camera stop failed: {data['message']}")
            return False
    except Exception as e:
        print(f"❌ Error stopping camera: {e}")
        return False

def simulate_test_alert():
    """Send a test alert to verify the system works"""
    print("🧪 Sending test alert...")
    try:
        test_data = {
            "message": "🚨 **REAL-TIME DETECTION TEST** 🚨\n\n✅ This test confirms that violence detection alerts will work during real-time camera monitoring!\n\n📹 When violence is detected by the camera, you will receive automatic alerts with video evidence."
        }
        
        response = requests.post(f"{BACKEND_URL}/test_telegram", json=test_data)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Test alert sent successfully!")
            print(f"   Phone: {data['debug_info']['phone_number']}")
            print(f"   Chat ID: {data['debug_info']['chat_id']}")
            return True
        else:
            print(f"❌ Test alert failed: {data['message']}")
            return False
    except Exception as e:
        print(f"❌ Error sending test alert: {e}")
        return False

def main():
    print("=" * 70)
    print("🎥 REAL-TIME VIOLENCE DETECTION ALERT TEST")
    print("=" * 70)
    
    # Step 1: Set up complete session
    if not setup_complete_session():
        print("\n❌ Failed to set up session. Please check backend and Telegram bot.")
        return
    
    # Step 2: Verify session status
    if not check_session_status():
        print("\n❌ Session not properly configured.")
        return
    
    # Step 3: Test camera start with session
    if not test_camera_start():
        print("\n❌ Camera start failed or alerts not enabled.")
        return
    
    # Step 4: Send test alert to confirm system works
    if not simulate_test_alert():
        print("\n❌ Test alert failed.")
        test_stop_camera()
        return
    
    # Step 5: Stop camera
    test_stop_camera()
    
    print("\n🎉 SUCCESS!")
    print("✅ Real-time violence detection alert system is working!")
    print("\n📋 **What this means:**")
    print("   ✅ User session is properly maintained")
    print("   ✅ Telegram connection is active")
    print("   ✅ Camera can start with alerts enabled")
    print("   ✅ Alert system is functional")
    print("\n🚨 **For real violence detection:**")
    print("   1. Go to: http://localhost:5500/front%20end/realtime.html")
    print("   2. Click 'Start Camera'")
    print("   3. Perform violent actions in front of camera")
    print("   4. You should receive automatic Telegram alerts!")
    
    print("\n" + "=" * 70)
    print("🎯 REAL-TIME ALERT TEST COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
