#!/usr/bin/env python3
"""
Fix Telegram Connection Script
This script helps users manually connect their phone number to their Telegram chat ID
"""

import requests
import json

# Configuration
BACKEND_URL = "http://localhost:5001"
TELEGRAM_BOT_TOKEN = "**********************************************"

def get_recent_telegram_activity():
    """Get recent Telegram bot activity"""
    print("🔍 Getting recent Telegram bot activity...")
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates"
        response = requests.get(url)
        data = response.json()
        
        if response.status_code == 200 and data.get('ok'):
            updates = data.get('result', [])
            print(f"✅ Found {len(updates)} recent interactions")
            
            if updates:
                print("\nRecent chat activity:")
                for i, update in enumerate(updates[-5:], 1):  # Show last 5
                    if 'message' in update:
                        msg = update['message']
                        chat_id = msg['chat']['id']
                        text = msg.get('text', 'N/A')
                        first_name = msg['chat'].get('first_name', 'N/A')
                        username = msg['chat'].get('username', 'N/A')
                        print(f"   {i}. Chat ID: {chat_id}")
                        print(f"      Name: {first_name}")
                        print(f"      Username: @{username}")
                        print(f"      Message: {text}")
                        print()
                
                return updates
            else:
                print("❌ No recent interactions found")
                return []
        else:
            print(f"❌ Error getting updates: {data}")
            return []
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def set_user_session(phone_number):
    """Set user session in backend"""
    print(f"📱 Setting user session for phone: {phone_number}")
    try:
        session_data = {
            "phone_number": phone_number,
            "full_name": "Test User",
            "email": "<EMAIL>",
            "location_name": "Test Location"
        }
        
        response = requests.post(f"{BACKEND_URL}/set_user_session", json=session_data)
        
        if response.status_code == 200:
            print("✅ User session set successfully")
            return True
        else:
            print(f"❌ Failed to set session: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Error setting session: {e}")
        return False

def manual_connect_phone_to_chat(phone_number, chat_id):
    """Manually connect phone to chat ID"""
    print(f"🔗 Connecting phone {phone_number} to chat ID {chat_id}")
    try:
        mapping_data = {
            "phone_number": phone_number,
            "chat_id": chat_id
        }
        
        response = requests.post(f"{BACKEND_URL}/map_phone_to_chat", json=mapping_data)
        
        if response.status_code == 200:
            print("✅ Phone mapped to chat ID successfully")
            return True
        else:
            print(f"❌ Failed to map phone: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Error mapping phone: {e}")
        return False

def test_telegram_connection(phone_number):
    """Test the Telegram connection"""
    print(f"🧪 Testing Telegram connection for {phone_number}")
    try:
        test_data = {
            "message": "🛡️ **TELEGRAM CONNECTION TEST** 🛡️\n\n✅ This is a test message from Vision Guard!\n\nIf you received this, your Telegram is connected successfully!"
        }
        
        response = requests.post(f"{BACKEND_URL}/test_telegram", json=test_data)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Test message sent successfully!")
            print(f"   Message: {data.get('message')}")
            return True
        else:
            print(f"❌ Test failed: {data.get('message')}")
            return False
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    print("=" * 60)
    print("🛡️ VISION GUARD TELEGRAM CONNECTION FIX")
    print("=" * 60)
    
    # Step 1: Get recent Telegram activity
    updates = get_recent_telegram_activity()
    
    if not updates:
        print("\n❌ No recent Telegram activity found!")
        print("Please:")
        print("1. Go to https://t.me/Visionguard_security_bot")
        print("2. Click START")
        print("3. Run this script again")
        return
    
    # Step 2: Get user's phone number
    print("\n📱 Enter your phone number (the one you registered with):")
    phone_number = input("Phone: ").strip()
    
    if not phone_number:
        print("❌ Phone number is required!")
        return
    
    # Step 3: Set user session
    if not set_user_session(phone_number):
        print("❌ Failed to set user session!")
        return
    
    # Step 4: Show available chat IDs and let user choose
    if updates:
        latest_update = updates[-1]
        if 'message' in latest_update:
            latest_chat_id = latest_update['message']['chat']['id']
            latest_name = latest_update['message']['chat'].get('first_name', 'Unknown')
            
            print(f"\n🤖 Most recent chat activity:")
            print(f"   Chat ID: {latest_chat_id}")
            print(f"   Name: {latest_name}")
            
            print(f"\nDo you want to connect your phone {phone_number} to this chat? (y/n)")
            choice = input("Choice: ").strip().lower()
            
            if choice == 'y':
                # Step 5: Connect phone to chat
                if manual_connect_phone_to_chat(phone_number, latest_chat_id):
                    # Step 6: Test the connection
                    if test_telegram_connection(phone_number):
                        print("\n🎉 SUCCESS!")
                        print("✅ Your Telegram is now connected to Vision Guard!")
                        print("✅ You will receive violence detection alerts automatically!")
                    else:
                        print("\n⚠️ Connection created but test failed.")
                        print("Please check your Telegram for messages.")
                else:
                    print("\n❌ Failed to create connection!")
            else:
                print("\n❌ Connection cancelled by user.")
    
    print("\n" + "=" * 60)
    print("🎯 TELEGRAM CONNECTION FIX COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
