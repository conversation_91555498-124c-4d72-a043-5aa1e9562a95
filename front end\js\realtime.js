document.addEventListener('DOMContentLoaded', () => {
    const videoFeed = document.getElementById('videoFeed');
    const startCameraBtn = document.getElementById('startCameraBtn');
    const stopCameraBtn = document.getElementById('stopCameraBtn');
    const refreshClipsBtn = document.getElementById('refreshClipsBtn');
    const testTelegramBtn = document.getElementById('testTelegramBtn');
    const status = document.getElementById('status');
    const telegramStatus = document.getElementById('telegramStatus');
    const violenceClipsSection = document.getElementById('violenceClipsSection');
    const clipsContainer = document.getElementById('clipsContainer');
    const violenceModal = document.getElementById('violenceModal');
    const violenceDescription = document.getElementById('violenceDescription');
    const closeModal = document.querySelector('.close-modal');

    let isAutoRecording = false;
    let clipsRefreshInterval = null;
    const API_BASE_URL = 'http://localhost:5001';

    // Check camera status on page load
    checkCameraStatus();

    // Function to check camera status
    async function checkCameraStatus() {
        try {
            const response = await fetch(`${API_BASE_URL}/camera_status`);
            const data = await response.json();

            if (data.status === 'running') {
                // Camera is already running, update UI
                videoFeed.src = `${API_BASE_URL}/video_feed`;
                startCameraBtn.style.display = 'none';
                stopCameraBtn.style.display = 'inline-block';
                refreshClipsBtn.style.display = 'inline-block';
                isAutoRecording = data.auto_recording || false;
                status.textContent = `Camera is running with violence detection${isAutoRecording ? ' (Auto-Recording)' : ''}`;
                status.className = 'status-message success';

                // Show clips section and start refreshing clips
                violenceClipsSection.style.display = 'block';
                startClipsRefresh();
            }
        } catch (error) {
            console.error('Error checking camera status:', error);
        }
    }

    // Function to load and display violence clips
    async function loadViolenceClips() {
        try {
            const response = await fetch(`${API_BASE_URL}/realtime_clips`);
            const data = await response.json();

            if (response.ok && data.clips) {
                displayClips(data.clips);
            } else {
                console.error('Failed to load clips:', data.message);
            }
        } catch (error) {
            console.error('Error loading clips:', error);
        }
    }

    // Function to display clips in the UI
    function displayClips(clips) {
        clipsContainer.innerHTML = '';

        if (clips.length === 0) {
            clipsContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">No violence clips detected yet.</p>';
            return;
        }

        clips.forEach(clip => {
            const clipElement = document.createElement('div');
            clipElement.className = 'clip-item';

            clipElement.innerHTML = `
                <video class="clip-video" controls>
                    <source src="${API_BASE_URL}${clip.video_url}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <div class="clip-info">
                    <span class="clip-timestamp">${clip.created_at}</span>
                    <span class="violence-indicator">Violence Detected</span>
                </div>
                ${clip.violence_description ? `
                    <div class="clip-description" onclick="showViolenceDescription('${clip.violence_description.replace(/'/g, "\\'")}')">
                        Click to view security report
                    </div>
                ` : ''}
            `;

            clipsContainer.appendChild(clipElement);
        });
    }

    // Function to show violence description in modal
    function showViolenceDescription(description) {
        violenceDescription.textContent = description;
        violenceModal.style.display = 'block';
    }

    // Function to start clips refresh interval
    function startClipsRefresh() {
        if (clipsRefreshInterval) {
            clearInterval(clipsRefreshInterval);
        }

        // Load clips immediately
        loadViolenceClips();

        // Refresh clips every 5 seconds
        clipsRefreshInterval = setInterval(loadViolenceClips, 5000);
    }

    // Function to stop clips refresh
    function stopClipsRefresh() {
        if (clipsRefreshInterval) {
            clearInterval(clipsRefreshInterval);
            clipsRefreshInterval = null;
        }
    }

    startCameraBtn.addEventListener('click', async () => {
        try {
            // Show loading state
            startCameraBtn.disabled = true;
            status.textContent = 'Starting camera and loading models...';
            status.className = 'status-message loading';

            const response = await fetch(`${API_BASE_URL}/start_camera`, {
                method: 'POST'
            });

            const data = await response.json();

            if (response.ok) {
                // Add a timestamp to prevent caching
                videoFeed.src = `${API_BASE_URL}/video_feed?t=${new Date().getTime()}`;
                startCameraBtn.style.display = 'none';
                stopCameraBtn.style.display = 'inline-block';
                refreshClipsBtn.style.display = 'inline-block';
                isAutoRecording = data.auto_recording || false;
                status.textContent = data.message || 'Camera started with violence detection and auto-recording';
                status.className = 'status-message success';

                // Show clips section and start refreshing clips
                violenceClipsSection.style.display = 'block';
                startClipsRefresh();
            } else {
                throw new Error(data.message || 'Failed to start camera');
            }
        } catch (error) {
            console.error('Error:', error);
            status.textContent = error.message || 'Failed to start camera';
            status.className = 'status-message error';
            startCameraBtn.disabled = false;
        }
    });

    stopCameraBtn.addEventListener('click', async () => {
        try {
            stopCameraBtn.disabled = true;
            status.textContent = 'Stopping camera...';

            const response = await fetch(`${API_BASE_URL}/stop_camera`, {
                method: 'POST'
            });

            const data = await response.json();

            if (response.ok) {
                videoFeed.src = '';
                startCameraBtn.style.display = 'inline-block';
                startCameraBtn.disabled = false;
                stopCameraBtn.style.display = 'none';
                refreshClipsBtn.style.display = 'none';
                isAutoRecording = false;
                status.textContent = data.message || 'Camera stopped';
                status.className = 'status-message';

                // Stop clips refresh and keep clips section visible
                stopClipsRefresh();
            } else {
                throw new Error(data.message || 'Failed to stop camera');
            }
        } catch (error) {
            console.error('Error:', error);
            status.textContent = error.message || 'Failed to stop camera';
            status.className = 'status-message error';
            stopCameraBtn.disabled = false;
        }
    });

    // Refresh clips button event handler
    refreshClipsBtn.addEventListener('click', async () => {
        try {
            refreshClipsBtn.disabled = true;
            status.textContent = 'Refreshing clips...';
            status.className = 'status-message loading';

            await loadViolenceClips();

            status.textContent = 'Clips refreshed successfully';
            status.className = 'status-message success';

            // Reset status after 2 seconds
            setTimeout(() => {
                if (isAutoRecording) {
                    status.textContent = 'Camera running with auto-recording';
                    status.className = 'status-message success';
                }
            }, 2000);
        } catch (error) {
            console.error('Error refreshing clips:', error);
            status.textContent = 'Failed to refresh clips';
            status.className = 'status-message error';
        } finally {
            refreshClipsBtn.disabled = false;
        }
    });

    // Telegram test functionality
    async function testTelegramConnection() {
        try {
            testTelegramBtn.disabled = true;
            testTelegramBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';

            const response = await fetch(`${API_BASE_URL}/test_telegram`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: '🚨 TEST ALERT: Telegram connection successful! You will receive violence detection alerts with video evidence.'
                })
            });

            const data = await response.json();

            if (response.ok) {
                telegramStatus.innerHTML = `
                    <div class="telegram-success">
                        <i class="fas fa-check-circle"></i>
                        <span>✅ Telegram connected! Test message sent successfully.</span>
                        <small>Phone: ${data.debug_info?.phone_number || 'N/A'}</small>
                    </div>
                `;
                status.textContent = 'Telegram test successful! You will receive violence detection alerts.';
                status.className = 'status-message success';
            } else {
                // If test failed, try to auto-connect first
                if (data.message.includes('not connected')) {
                    status.textContent = 'Attempting automatic connection...';
                    status.className = 'status-message loading';

                    try {
                        const connectResponse = await fetch(`${API_BASE_URL}/connect_telegram`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                phone_number: JSON.parse(localStorage.getItem('currentUser') || '{}').phone
                            })
                        });

                        const connectData = await connectResponse.json();

                        if (connectResponse.ok) {
                            telegramStatus.innerHTML = `
                                <div class="telegram-success">
                                    <i class="fas fa-check-circle"></i>
                                    <span>✅ Telegram auto-connected! Test message sent successfully.</span>
                                </div>
                            `;
                            status.textContent = 'Telegram auto-connected! You will receive violence detection alerts.';
                            status.className = 'status-message success';
                        } else {
                            throw new Error(connectData.message);
                        }
                    } catch (connectError) {
                        // Try quick connect as final fallback
                        try {
                            status.textContent = 'Trying quick connect...';
                            const quickResponse = await fetch(`${API_BASE_URL}/quick_connect_telegram`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({})
                            });

                            const quickData = await quickResponse.json();

                            if (quickResponse.ok) {
                                telegramStatus.innerHTML = `
                                    <div class="telegram-success">
                                        <i class="fas fa-check-circle"></i>
                                        <span>✅ Telegram quick-connected! Test message sent.</span>
                                        <small>Connected using recent bot activity</small>
                                    </div>
                                `;
                                status.textContent = 'Telegram quick-connected! You will receive violence detection alerts.';
                                status.className = 'status-message success';
                            } else {
                                throw new Error(quickData.message);
                            }
                        } catch (quickError) {
                            telegramStatus.innerHTML = `
                                <div class="telegram-error">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>❌ ${data.message}</span>
                                    <a href="https://t.me/Visionguard_security_bot" target="_blank" class="bot-link">Start Bot First</a>
                                    <button onclick="tryQuickConnect()" class="quick-connect-btn">Quick Connect</button>
                                </div>
                            `;
                            status.textContent = 'Please start the Telegram bot first, then try again.';
                            status.className = 'status-message error';
                        }
                    }
                } else {
                    telegramStatus.innerHTML = `
                        <div class="telegram-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>❌ ${data.message}</span>
                            ${data.bot_link ? `<a href="${data.bot_link}" target="_blank" class="bot-link">Start Bot</a>` : ''}
                        </div>
                    `;
                    status.textContent = 'Telegram connection failed. Please start the bot first.';
                    status.className = 'status-message error';
                }
            }
        } catch (error) {
            console.error('Error testing Telegram:', error);
            telegramStatus.innerHTML = `
                <div class="telegram-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>❌ Connection test failed. Please try again.</span>
                </div>
            `;
            status.textContent = 'Telegram test failed. Please check your connection.';
            status.className = 'status-message error';
        } finally {
            testTelegramBtn.disabled = false;
            testTelegramBtn.innerHTML = '<i class="fab fa-telegram"></i> Test Telegram Alerts';
        }
    }

    // Add Telegram test event listener
    testTelegramBtn.addEventListener('click', testTelegramConnection);

    // Function to check Telegram connection status
    async function checkTelegramStatus() {
        try {
            // Get current user from localStorage
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

            if (!currentUser.phone) {
                telegramStatus.innerHTML = `
                    <div class="telegram-info">
                        <i class="fab fa-telegram"></i>
                        <span>Please login first to connect Telegram alerts</span>
                        <a href="login.html" class="bot-link">Login</a>
                    </div>
                `;
                return;
            }

            const response = await fetch(`${API_BASE_URL}/check_telegram_status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phone_number: currentUser.phone
                })
            });

            const data = await response.json();

            if (response.ok) {
                if (data.is_linked) {
                    telegramStatus.innerHTML = `
                        <div class="telegram-success">
                            <i class="fas fa-check-circle"></i>
                            <span>✅ Telegram connected! You will receive violence detection alerts.</span>
                            <small>Phone: ${currentUser.phone}</small>
                        </div>
                    `;
                } else {
                    telegramStatus.innerHTML = `
                        <div class="telegram-info">
                            <i class="fab fa-telegram"></i>
                            <div class="telegram-instructions">
                                <h4>🚨 Connect Telegram for Violence Alerts</h4>
                                <p>Get instant notifications with video evidence when violence is detected</p>
                                <div class="steps">
                                    <div class="step">
                                        <span class="step-number">1</span>
                                        <span>Click "Start Bot" below</span>
                                    </div>
                                    <div class="step">
                                        <span class="step-number">2</span>
                                        <span>Tap "START" in Telegram</span>
                                    </div>
                                    <div class="step">
                                        <span class="step-number">3</span>
                                        <span>Click "Check Connection"</span>
                                    </div>
                                </div>
                                <div class="telegram-actions">
                                    <a href="https://t.me/Visionguard_security_bot" target="_blank" class="bot-link" onclick="showBotInstructions()">
                                        <i class="fab fa-telegram"></i> Start Bot
                                    </a>
                                    <button onclick="checkTelegramStatus()" class="refresh-btn">
                                        <i class="fas fa-refresh"></i> Check Connection
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } else {
                telegramStatus.innerHTML = `
                    <div class="telegram-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>❌ Error checking Telegram status</span>
                        <a href="https://t.me/Visionguard_security_bot" target="_blank" class="bot-link">
                            Start Bot
                        </a>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error checking Telegram status:', error);
            telegramStatus.innerHTML = `
                <div class="telegram-info">
                    <i class="fab fa-telegram"></i>
                    <span>Connect Telegram to receive instant violence detection alerts with video evidence</span>
                    <a href="https://t.me/Visionguard_security_bot" target="_blank" class="bot-link">
                        Start Bot
                    </a>
                </div>
            `;
        }
    }

    // Function to set user session on backend
    async function setUserSession() {
        try {
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

            if (currentUser.phone) {
                await fetch(`${API_BASE_URL}/set_user_session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone_number: currentUser.phone,
                        full_name: currentUser.fullName || 'User',
                        email: currentUser.email || '',
                        location_name: 'Security Camera Location'
                    })
                });
                console.log('User session set for:', currentUser.phone);
            }
        } catch (error) {
            console.error('Error setting user session:', error);
        }
    }

    // Initialize page
    async function initializePage() {
        await setUserSession();
        await checkTelegramStatus();
    }

    // Initialize when page loads
    initializePage();

    // Modal close event handlers
    closeModal.addEventListener('click', () => {
        violenceModal.style.display = 'none';
    });

    window.addEventListener('click', (event) => {
        if (event.target === violenceModal) {
            violenceModal.style.display = 'none';
        }
    });

    // Function to show bot instructions
    function showBotInstructions() {
        // Update status to show instructions
        status.innerHTML = `
            <div class="bot-instructions">
                <h4>📱 Telegram Bot Instructions</h4>
                <p><strong>The bot should open in a new tab. Follow these steps:</strong></p>
                <ol>
                    <li>In the Telegram bot tab, click the <strong>"START"</strong> button</li>
                    <li>You'll receive a welcome message from Vision Guard</li>
                    <li>Come back here and click <strong>"Check Connection"</strong></li>
                    <li>You're done! You'll receive violence alerts automatically</li>
                </ol>
                <p><em>If the bot didn't open, <a href="https://t.me/Visionguard_security_bot" target="_blank">click here</a></em></p>
            </div>
        `;
        status.className = 'status-message info';

        // Auto-check connection after 10 seconds
        setTimeout(() => {
            checkTelegramStatus();
        }, 10000);
    }

    // Quick connect function
    async function tryQuickConnect() {
        try {
            status.textContent = 'Attempting quick connect...';
            status.className = 'status-message loading';

            const response = await fetch(`${API_BASE_URL}/quick_connect_telegram`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });

            const data = await response.json();

            if (response.ok) {
                telegramStatus.innerHTML = `
                    <div class="telegram-success">
                        <i class="fas fa-check-circle"></i>
                        <span>✅ Quick connect successful! Test message sent.</span>
                        <small>Phone: ${data.phone_number}, Chat ID: ${data.chat_id}</small>
                    </div>
                `;
                status.textContent = 'Telegram quick-connected! You will receive violence detection alerts.';
                status.className = 'status-message success';
            } else {
                telegramStatus.innerHTML = `
                    <div class="telegram-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>❌ Quick connect failed: ${data.message}</span>
                        <a href="https://t.me/Visionguard_security_bot" target="_blank" class="bot-link">Start Bot</a>
                    </div>
                `;
                status.textContent = 'Quick connect failed. Please start the bot first.';
                status.className = 'status-message error';
            }
        } catch (error) {
            console.error('Error in quick connect:', error);
            status.textContent = 'Quick connect failed. Please try again.';
            status.className = 'status-message error';
        }
    }

    // Make functions globally accessible for inline onclick
    window.showViolenceDescription = showViolenceDescription;
    window.checkTelegramStatus = checkTelegramStatus;
    window.showBotInstructions = showBotInstructions;
    window.tryQuickConnect = tryQuickConnect;

    // Add event listener for video feed errors
    videoFeed.addEventListener('error', (e) => {
        console.error('Video feed error:', e);
        status.textContent = 'Error loading video feed. Please try again.';
        status.className = 'status-message error';
        startCameraBtn.style.display = 'inline-block';
        startCameraBtn.disabled = false;
        stopCameraBtn.style.display = 'none';
        recordBtn.style.display = 'none';

        // Try to stop the camera on the backend
        fetch(`${API_BASE_URL}/stop_camera`, {
            method: 'POST'
        }).catch(err => console.error('Error stopping camera after video feed error:', err));
    });

    // Add a retry button
    const retryBtn = document.createElement('button');
    retryBtn.textContent = 'Retry Connection';
    retryBtn.className = 'control-btn';
    retryBtn.style.display = 'none';
    document.querySelector('.controls').appendChild(retryBtn);

    retryBtn.addEventListener('click', async () => {
        retryBtn.style.display = 'none';
        startCameraBtn.click();
    });

    // Function to check server health
    async function checkServerHealth() {
        try {
            const response = await fetch(`${API_BASE_URL}/health`);
            if (response.ok) {
                console.log('Server is healthy');
                return true;
            } else {
                console.error('Server health check failed');
                status.textContent = 'Server is not responding properly. Please check the backend server.';
                status.className = 'status-message error';
                retryBtn.style.display = 'inline-block';
                return false;
            }
        } catch (error) {
            console.error('Server health check error:', error);
            status.textContent = 'Cannot connect to server. Please check if the backend is running.';
            status.className = 'status-message error';
            retryBtn.style.display = 'inline-block';
            return false;
        }
    }

    // Check server health on page load
    checkServerHealth();
});

