#!/usr/bin/env python3
"""
Simulate Violence Detection
This script simulates the actual violence detection process and triggers real alerts
"""

import requests
import json
import time
import os

# Configuration
BACKEND_URL = "http://localhost:5001"

def setup_user_session():
    """Set up user session for testing"""
    print("📱 Setting up user session...")
    try:
        session_data = {
            "phone_number": "01001747852",
            "full_name": "Test User",
            "email": "<EMAIL>",
            "location_name": "Test Security Camera Location"
        }
        
        response = requests.post(f"{BACKEND_URL}/set_user_session", json=session_data)
        
        if response.status_code == 200:
            print("✅ User session set successfully")
            return True
        else:
            print(f"❌ Failed to set session: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Error setting session: {e}")
        return False

def ensure_telegram_connection():
    """Ensure Telegram is connected"""
    print("🤖 Ensuring Telegram connection...")
    try:
        # Try quick connect
        response = requests.post(f"{BACKEND_URL}/quick_connect_telegram", json={})
        data = response.json()
        
        if response.status_code == 200:
            print(f"✅ Telegram connected to chat ID: {data['chat_id']}")
            return True
        else:
            print(f"❌ Telegram connection failed: {data['message']}")
            return False
    except Exception as e:
        print(f"❌ Error connecting Telegram: {e}")
        return False

def trigger_violence_alert():
    """Trigger a simulated violence detection alert"""
    print("🚨 Triggering violence detection alert...")
    try:
        # Create a realistic violence description
        violence_description = """🚨 SECURITY ALERT: Aggressive behavior detected in monitored area. 
        
Two individuals observed in physical altercation with rapid, aggressive movements. 
Immediate security response recommended to assess and de-escalate situation."""
        
        # Simulate the emergency alert that would be sent during real violence detection
        alert_data = {
            "message": f"""🚨 **VISION GUARD EMERGENCY ALERT** 🚨

⚠️ **VIOLENCE DETECTED - IMMEDIATE ATTENTION REQUIRED** ⚠️

🆔 **Alert ID:** VG{int(time.time())}
📍 **Location:** Test Security Camera Location
📅 **Date:** {time.strftime("%B %d, %Y")}
🕐 **Time:** {time.strftime("%H:%M:%S")}

🤖 **AI SECURITY ANALYSIS:**
{violence_description}

📹 **Evidence:** Video footage being processed
🔗 **Live Feed:** http://localhost:5500/front%20end/realtime.html

⚡ **AUTOMATED RESPONSE ACTIVATED**
• Security protocols engaged
• Evidence being processed
• Authorities may be notified

🚨 **ACTION REQUIRED:**
Please verify the situation and take appropriate action immediately.

---
🛡️ Vision Guard AI Security System
Protecting What Matters Most"""
        }
        
        response = requests.post(f"{BACKEND_URL}/test_telegram", json=alert_data)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Violence detection alert sent successfully!")
            print(f"   Sent to phone: {data['debug_info']['phone_number']}")
            print(f"   Chat ID: {data['debug_info']['chat_id']}")
            print("📱 Check your Telegram for the emergency alert!")
            return True
        else:
            print(f"❌ Failed to send violence alert: {data['message']}")
            return False
    except Exception as e:
        print(f"❌ Error triggering violence alert: {e}")
        return False

def test_realtime_system():
    """Test if the realtime system would work"""
    print("🔍 Testing realtime violence detection system readiness...")
    try:
        # Check session status
        response = requests.get(f"{BACKEND_URL}/debug_session")
        data = response.json()
        
        if response.status_code == 200:
            session = data['current_user_session']
            phone_mapped = data['phone_is_mapped']
            
            print("📊 System Status:")
            print(f"   📱 Phone Number: {session.get('phone_number', 'Not set')}")
            print(f"   👤 User Name: {session.get('full_name', 'Not set')}")
            print(f"   💬 Telegram Connected: {'Yes' if phone_mapped else 'No'}")
            print(f"   📍 Location: {session.get('location_name', 'Not set')}")
            
            if session.get('phone_number') and phone_mapped:
                print("✅ Realtime violence detection system is READY!")
                print("   When violence is detected by the camera, alerts will be sent automatically.")
                return True
            else:
                print("⚠️ Realtime system needs setup:")
                if not session.get('phone_number'):
                    print("   - User session not set")
                if not phone_mapped:
                    print("   - Telegram not connected")
                return False
        else:
            print(f"❌ Failed to check system status: {data}")
            return False
    except Exception as e:
        print(f"❌ Error checking system status: {e}")
        return False

def main():
    print("=" * 70)
    print("🚨 VISION GUARD VIOLENCE DETECTION SIMULATION")
    print("=" * 70)
    
    # Step 1: Set up user session
    if not setup_user_session():
        print("\n❌ Failed to set up user session.")
        return
    
    # Step 2: Ensure Telegram connection
    if not ensure_telegram_connection():
        print("\n❌ Failed to connect Telegram.")
        print("💡 Please start the Telegram bot first: https://t.me/Visionguard_security_bot")
        return
    
    # Step 3: Test system readiness
    if not test_realtime_system():
        print("\n❌ System not ready for realtime detection.")
        return
    
    # Step 4: Simulate violence detection
    print(f"\n🚨 Simulating violence detection in 3 seconds...")
    time.sleep(1)
    print("3...")
    time.sleep(1)
    print("2...")
    time.sleep(1)
    print("1...")
    time.sleep(1)
    
    if trigger_violence_alert():
        print("\n🎉 SUCCESS!")
        print("✅ Violence detection alert simulation complete!")
        print("📱 You should have received a Telegram alert!")
        print("\n🔄 **For Real Violence Detection:**")
        print("   1. Go to the realtime page: http://localhost:5500/front%20end/realtime.html")
        print("   2. Click 'Start Camera'")
        print("   3. When violence is detected, you'll get automatic alerts!")
    else:
        print("\n❌ Violence alert simulation failed.")
    
    print("\n" + "=" * 70)
    print("🎯 VIOLENCE DETECTION SIMULATION COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
