#!/usr/bin/env python3
"""
Test Violence Detection Alerts
This script tests the complete flow from user session setup to violence detection alerts
"""

import requests
import json
import time

# Configuration
BACKEND_URL = "http://localhost:5001"
TELEGRAM_BOT_TOKEN = "**********************************************"

def test_backend_connection():
    """Test if backend is running"""
    print("🔍 Testing backend connection...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False

def setup_user_session(phone_number="01001747852", name="Test User"):
    """Set up user session"""
    print(f"📱 Setting up user session for {phone_number}...")
    try:
        session_data = {
            "phone_number": phone_number,
            "full_name": name,
            "email": "<EMAIL>",
            "location_name": "Test Security Location"
        }
        
        response = requests.post(f"{BACKEND_URL}/set_user_session", json=session_data)
        
        if response.status_code == 200:
            print("✅ User session set successfully")
            return True
        else:
            print(f"❌ Failed to set session: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Error setting session: {e}")
        return False

def check_session_debug():
    """Check current session status"""
    print("🔍 Checking session debug info...")
    try:
        response = requests.get(f"{BACKEND_URL}/debug_session")
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Session debug info retrieved:")
            print(f"   Phone: {data['current_user_session'].get('phone_number', 'Not set')}")
            print(f"   Name: {data['current_user_session'].get('full_name', 'Not set')}")
            print(f"   Chat ID: {data['current_user_session'].get('telegram_chat_id', 'Not set')}")
            print(f"   Phone Mapped: {data['phone_is_mapped']}")
            print(f"   Total Mappings: {len(data['phone_to_chat_mapping'])}")
            return data
        else:
            print(f"❌ Failed to get debug info: {data}")
            return None
    except Exception as e:
        print(f"❌ Error getting debug info: {e}")
        return None

def setup_telegram_connection(phone_number="01001747852"):
    """Set up Telegram connection"""
    print(f"🤖 Setting up Telegram connection for {phone_number}...")
    try:
        # First try quick connect
        response = requests.post(f"{BACKEND_URL}/quick_connect_telegram", json={})
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Quick connect successful!")
            print(f"   Chat ID: {data['chat_id']}")
            return data['chat_id']
        else:
            print(f"⚠️ Quick connect failed: {data['message']}")
            
            # Try regular connect
            response = requests.post(f"{BACKEND_URL}/connect_telegram", json={})
            data = response.json()
            
            if response.status_code == 200:
                print("✅ Regular connect successful!")
                return data['chat_id']
            else:
                print(f"❌ Regular connect failed: {data['message']}")
                return None
    except Exception as e:
        print(f"❌ Error connecting Telegram: {e}")
        return None

def test_telegram_alert():
    """Test sending a Telegram alert"""
    print("🧪 Testing Telegram alert...")
    try:
        test_data = {
            "message": "🚨 **VIOLENCE DETECTION TEST** 🚨\n\n✅ This is a test of the violence detection alert system!\n\nIf you received this, alerts will work when violence is detected."
        }
        
        response = requests.post(f"{BACKEND_URL}/test_telegram", json=test_data)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Test alert sent successfully!")
            print(f"   Phone: {data['debug_info']['phone_number']}")
            print(f"   Chat ID: {data['debug_info']['chat_id']}")
            return True
        else:
            print(f"❌ Test alert failed: {data['message']}")
            if 'debug_info' in data:
                print(f"   Debug info: {data['debug_info']}")
            return False
    except Exception as e:
        print(f"❌ Error testing alert: {e}")
        return False

def simulate_violence_detection():
    """Simulate a violence detection event"""
    print("🚨 Simulating violence detection...")
    try:
        # This would normally be triggered by the violence detection system
        # For testing, we'll call the emergency alert function directly
        
        # Note: This is a simulation - in real usage, violence detection triggers this automatically
        print("⚠️ Note: In real usage, this is triggered automatically when violence is detected")
        print("   For now, we're testing the Telegram connection with the test function")
        
        return test_telegram_alert()
    except Exception as e:
        print(f"❌ Error simulating violence detection: {e}")
        return False

def main():
    print("=" * 70)
    print("🛡️ VISION GUARD VIOLENCE DETECTION ALERT TEST")
    print("=" * 70)
    
    # Step 1: Test backend connection
    if not test_backend_connection():
        print("\n❌ Backend is not running. Please start the backend first.")
        return
    
    # Step 2: Set up user session
    if not setup_user_session():
        print("\n❌ Failed to set up user session.")
        return
    
    # Step 3: Check session debug info
    debug_info = check_session_debug()
    if not debug_info:
        print("\n❌ Failed to get session debug info.")
        return
    
    # Step 4: Set up Telegram connection
    chat_id = setup_telegram_connection()
    if not chat_id:
        print("\n❌ Failed to set up Telegram connection.")
        print("💡 Please:")
        print("   1. Go to https://t.me/Visionguard_security_bot")
        print("   2. Click START")
        print("   3. Run this script again")
        return
    
    # Step 5: Test Telegram alert
    if test_telegram_alert():
        print("\n🎉 SUCCESS!")
        print("✅ Complete violence detection alert system is working!")
        print("✅ When violence is detected, you will receive:")
        print("   📱 Instant Telegram notification")
        print("   📹 Video evidence")
        print("   📍 Location and timestamp")
        print("   🤖 AI security analysis")
    else:
        print("\n❌ Telegram alert test failed.")
        print("💡 Check the debug info above for troubleshooting.")
    
    print("\n" + "=" * 70)
    print("🎯 VIOLENCE DETECTION ALERT TEST COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
