#!/usr/bin/env python3
"""
Simple test script to verify Telegram bot token and functionality
"""

import requests
import json

# Test the bot token directly
TELEGRAM_BOT_TOKEN = '**********************************************'

def test_bot_token():
    """Test if bot token is valid"""
    print('🔍 Testing bot token validity...')
    try:
        url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getMe'
        response = requests.get(url)
        data = response.json()
        
        if response.status_code == 200 and data.get('ok'):
            print('✅ Bot token is valid!')
            print(f'   Bot name: {data["result"]["first_name"]}')
            print(f'   Bot username: @{data["result"]["username"]}')
            return True
        else:
            print(f'❌ Bot token invalid: {data}')
            return False
    except Exception as e:
        print(f'❌ Error testing bot token: {e}')
        return False

def get_recent_updates():
    """Get recent bot updates"""
    print('\n🔍 Getting recent bot updates...')
    try:
        url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates'
        response = requests.get(url)
        data = response.json()
        
        if response.status_code == 200 and data.get('ok'):
            updates = data.get('result', [])
            print(f'✅ Found {len(updates)} recent updates')
            
            if updates:
                print('Recent interactions:')
                for update in updates[-3:]:  # Show last 3
                    if 'message' in update:
                        msg = update['message']
                        chat_id = msg['chat']['id']
                        text = msg.get('text', 'N/A')
                        username = msg['chat'].get('username', 'N/A')
                        first_name = msg['chat'].get('first_name', 'N/A')
                        print(f'   Chat ID: {chat_id}, Name: {first_name}, Username: @{username}, Text: {text}')
                return updates
            else:
                print('   No recent interactions found')
                return []
        else:
            print(f'❌ Error getting updates: {data}')
            return []
    except Exception as e:
        print(f'❌ Error getting updates: {e}')
        return []

def test_send_message(chat_id):
    """Test sending a message to a specific chat ID"""
    print(f'\n🔍 Testing message sending to chat ID: {chat_id}...')
    try:
        url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage'
        payload = {
            'chat_id': chat_id,
            'text': '🛡️ **VISION GUARD TEST** 🛡️\n\nThis is a test message from your Vision Guard security system!\n\n✅ Telegram connection is working correctly.',
            'parse_mode': 'Markdown'
        }
        
        response = requests.post(url, json=payload)
        data = response.json()
        
        if response.status_code == 200 and data.get('ok'):
            print('✅ Test message sent successfully!')
            return True
        else:
            print(f'❌ Failed to send message: {data}')
            return False
    except Exception as e:
        print(f'❌ Error sending message: {e}')
        return False

def main():
    print("=" * 60)
    print("🛡️ VISION GUARD TELEGRAM BOT TEST")
    print("=" * 60)
    
    # Test 1: Verify bot token
    if not test_bot_token():
        print("\n❌ Bot token test failed. Cannot proceed.")
        return
    
    # Test 2: Get recent updates
    updates = get_recent_updates()
    
    # Test 3: Try to send a test message if we have recent interactions
    if updates:
        print("\n🔍 Found recent interactions. Testing message sending...")
        for update in updates[-1:]:  # Test with most recent chat
            if 'message' in update:
                chat_id = update['message']['chat']['id']
                success = test_send_message(chat_id)
                if success:
                    print(f"✅ Successfully sent test message to chat ID: {chat_id}")
                    break
    else:
        print("\n⚠️ No recent interactions found.")
        print("To test message sending:")
        print("1. Go to https://t.me/Visionguard_security_bot")
        print("2. Click START")
        print("3. Run this script again")
    
    print("\n" + "=" * 60)
    print("🎯 TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
